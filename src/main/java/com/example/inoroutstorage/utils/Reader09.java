package com.example.inoroutstorage.utils;


import java.util.ArrayList;
import java.util.List;

public class Reader09 {
	public static boolean isOk = true;

    public static void main(String[] args) {
        ReadCard();
    }
	public static List<String>  ReadCard() {

		UHFReader09 reader = UHFReader09.INSTANCE;

		int[] Port = new int[3];// com1
		byte[] comAddr = new byte[1];
		comAddr[0] = (byte) 255;
		byte baud = 5;// 57600bps
		int[] PortHandle = new int[1];
		int frmcomportindex;

		int m = 0, EPClen;
		String sEPC;
		List<String> epc = new ArrayList<>();

		int result = reader.AutoOpenComPort(Port, comAddr, baud, PortHandle);
		// int result = reader.OpenComPort(3, comAddr, baud, PortHandle);
		if (result == 0) {

				byte AdrTID = 0;
				byte LenTID = 0;
				byte TIDFlag = 0;
				byte[] EPC = new byte[5000];
				int[] Totallen = new int[1];
				int[] CardNum = new int[1];
				frmcomportindex = PortHandle[0];
			while (isOk){
				System.out.println(isOk+"1");
				result = reader.Inventory_G2(comAddr, AdrTID, LenTID, TIDFlag, EPC, Totallen, CardNum, frmcomportindex);
				System.out.println(result);
				if ((result == 1) | (result == 2) | (result == 3) | (result == 4) | (result == 0xFB)) {
					byte[] daw = new byte[Totallen[0]];
					System.arraycopy(EPC, 0, daw, 0, Totallen[0]);
					String temps = ByteArrayToHexString(daw);
					// daw 装hex
					if (CardNum[0] == 0) {
						// 继续扫描
						System.out.println("未扫描到标签");
						try {
							Thread.sleep(500);
						} catch (InterruptedException e) {
							e.printStackTrace();
						}
						continue;
//					reader.CloseComPort();
//					ReadCard();
					}
					for (int CardIndex = 0; CardIndex < CardNum[0]; CardIndex++) {
						EPClen = daw[m];
						sEPC = temps.substring(m * 2 + 2,m * 2 + 2 + EPClen * 2);
						m = m + EPClen + 1;
						if (sEPC.length() != EPClen * 2) {
							System.out.println(sEPC);
							System.out.println("数据异常");
						}
						System.out.println(sEPC);
						epc.add(sEPC);
//					System.out.println(sEPC);
					}
					reader.CloseComPort();
					return epc;
				}
			}
			reader.CloseComPort();
		} else {
			System.out.println("连接失败"+result);
			return null;
		}
		reader.CloseComPort();
		return epc;
	}


	private static String ByteArrayToHexString(byte[] data)
     {
         StringBuilder sb = new StringBuilder(data.length * 3);
         for (byte b : data){
        	 sb.append(StringUtil.byteToHex(b));
         }
         return sb.toString().toUpperCase();

     }
}
