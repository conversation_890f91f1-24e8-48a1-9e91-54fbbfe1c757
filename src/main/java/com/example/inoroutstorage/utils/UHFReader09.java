package com.example.inoroutstorage.utils;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Platform;

public interface UHFReader09 extends Library {
	// DLL文件默认路径为项目根目录，若DLL文件存放在项目外，请使用绝对路径。（此处：(Platform.isWindows()?"msvcrt":"c")指本地动态库msvcrt.dll）
	UHFReader09 INSTANCE = (UHFReader09) Native.loadLibrary((Platform.isWindows() ? "UHFReader09" : "c"),
			UHFReader09.class);

	int AutoOpenComPort(int[] port, byte[] comAddr, byte baud, int[] PortHandle);

	int OpenComPort(int port, byte[] comAddr, byte baud, int[] PortHandle);

	int CloseComPort();

	int CloseSpecComPort(int port);

	int Inventory_G2(byte[] fComAdr, byte AdrTID, byte LenTID, byte TIDFlag, byte[] EPC, int[] Totallen, int[] CardNum, int frmcomportindex);


}
