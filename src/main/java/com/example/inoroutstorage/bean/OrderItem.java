package com.example.inoroutstorage.bean;

import lombok.Data;

import java.util.List;

@Data
public class OrderItem {
    private String sn;
    private String goodsId;
    private String goodsName;
    private String skuId;
    private Integer goodsNum;
    private Integer returnNum;
    private Integer returnFinishedNum;
    private Integer repairNum;
    private Integer curReturnNum;
    private Integer transferNum;
    private Integer deferNum;
    private Integer optReturnNum;
    private Integer optDeliverNum;
    private Integer unReturnNum;
    private String image;
    private Double goodsPrice;
    private String beginTime;
    private String endTime;
    private String modelCode;
    private String attribute;
    private boolean checked;
    private String remark;
    private String afterSaleStatus;
    private String complainStatus;
    private String commentStatus;
    private Integer unsentNum;
    private String labelId;
    private List<String> sentOutCodeList;
    private List<ReturnGoods> unReturngoodsAssets;
}
