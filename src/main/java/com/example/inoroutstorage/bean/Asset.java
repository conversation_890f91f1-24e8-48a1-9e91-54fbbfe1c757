package com.example.inoroutstorage.bean;

import lombok.Data;

/**
 * @ClassName Asset * @Description TODO
 * <AUTHOR>
 * @Date 14:47 2024/10/24
 * @Version 1.0
 **/
@Data
public class Asset {
    private String assetId;     // 资产id
    private String goodsName;   // 设备名称
    private String goodsId;     // 商品id
    private String skuId;       // skuId
    private String outCode;     // 外编号（标签上打印的编号）
    private String inCode;      // 内编号
    private String labelId;     // 标签id（PDA识别的码）
    private String modelCode;   // 型号
    private String status;       // 状态
    private String statusDesc;   // 状态描述
    private Integer goodsNum;   // 状态描述



    private int countInfo;   // 数量信息
}
