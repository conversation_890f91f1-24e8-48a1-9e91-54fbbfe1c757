package com.example.inoroutstorage.bean;

import lombok.Data;

import java.util.List;

@Data
public class Result {
    private List<Order> records;
    private int total;
    private int size;
    private int current;
    private List<Object> orders; // Assuming orders can be of any type, use Object or define a specific class
    private boolean optimizeCountSql;
    private boolean searchCount;
    private Object countId; // Adjust the type if you know what countId represents
    private Object maxLimit; // Adjust the type if you know what maxLimit represents
    private int pages;
}