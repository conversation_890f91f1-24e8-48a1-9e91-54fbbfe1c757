package com.example.inoroutstorage.netty;

import com.example.inoroutstorage.reader.ReadStart;

/**
 * @ClassName ThreadSend * @Description TODO
 * <AUTHOR>
 * @Date 10:04 2024/10/19
 * @Version 1.0
 **/
public class ThreadSend extends Thread {

    private String stashName;
    private String stashType;
    private String type;

    public ThreadSend(String stashName, String stashType, String type) {
        this.stashName = stashName;
        this.stashType = stashType;
        this.type = type;
    }

//    private final String startCode = "0105000100009C0A";
    private final String redLineStartCode = "01050000FF008C3A";
    private final String greenLineStartCode = "01050001FF00DDFA";
//       StartApplication.clientError.sendCommand(stashName,stashType,startCode); //on
//        StartApplication.clientError.sendCommand(stashName,stashType,LineStartCode);//on

//    private final String stopCode = "01050001FF00DDFA"; //off
    private final String redlineStopCode = "010500000000CDCA";//off
    private final String greenlineStopCode = "0105000100009C0A";//off


    @Override
    public void run() {
        if (type.equals("off")) {
            ReadStart.clientError.sendCommand(stashName, stashType, redlineStopCode);
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ReadStart.clientError.sendCommand(stashName, stashType, greenLineStartCode);

        } else if (type.equals("on")) {
            ReadStart.clientError.sendCommand(stashName, stashType, greenlineStopCode); //on
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ReadStart.clientError.sendCommand(stashName, stashType, redLineStartCode);//on
        } else {
//            ReadStart.clientError.sendCommand(stashName, stashType, stopCode);
        }

    }
}
