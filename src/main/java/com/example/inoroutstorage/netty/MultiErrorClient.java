package com.example.inoroutstorage.netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.util.AttributeKey;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MultiErrorClient {
    private final Map<String, Channel> channelMap = new ConcurrentHashMap<>();

    private static final int RECONNECT_DELAY = 5;  // 断线重连的时间间隔，单位：秒
    private final EventLoopGroup group = new NioEventLoopGroup();
    private final Bootstrap bootstrap = new Bootstrap();
    private final List<Map<String, Object>> scanners = new ArrayList<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);

    public void addScanner(Map<String, Object> falEquipmentEntity) {
        scanners.add(falEquipmentEntity);
    }

    public void start() {
        try {
            bootstrap.group(group)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) {
                            ChannelPipeline pipeline = ch.pipeline();
                            pipeline.addLast(new LoggingHandler(LogLevel.INFO));
                            pipeline.addLast(new StringDecoder());
                            pipeline.addLast(new StringEncoder());
                            pipeline.addLast(new ScannerClientHandler());
                        }
                    });

            for (Map<String, Object> scanner : scanners) {
                connect(scanner);
            }
        } finally {
            Runtime.getRuntime().addShutdownHook(new Thread(() -> group.shutdownGracefully()));
        }
    }

    public void connect(Map<String, Object> scanner) {
        bootstrap.connect(scanner.get("lightIp").toString(), Integer.parseInt(scanner.get("lightProt").toString())).addListener((ChannelFutureListener) future -> {

            if (future.isSuccess()) {
                System.out.println("连接成功到扫码枪: " + scanner.get("lightIp").toString());
                channelMap.put(scanner.get("lightIp").toString() + ":" + scanner.get("lightProt").toString(), future.channel());  // 存储 Channel
                future.channel().attr(AttributeKey.valueOf("scannerInfo")).set(scanner);
            } else {
                // 连接失败处理逻辑
            }
        });
    }

    private class ScannerClientHandler extends SimpleChannelInboundHandler<String> {

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, String msg) {
            Map<String, Object> equipmentEntity = (Map<String, Object>) ctx.channel().attr(AttributeKey.valueOf("scannerInfo")).get();
//            String key = equipmentEntity.getStashName();

//            Channel channel = ctx.channel();
//            if (channel != null && channel.isActive()) {
//                String command = "010500000000CDCA";
//                byte[] bytes = hexStringToByteArray(command);
//
//                // 通过Netty发送字节数组
//                channel.writeAndFlush(channel.alloc().buffer().writeBytes(bytes));
//                System.out.println("指令发送成功: " + command + " 给设备: ");
//            } else {
//                System.err.println("设备未连接: ");
//            }
            System.out.println("接收到的数据: " + msg + " 来自: " + equipmentEntity.get("lightIp").toString());
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) {
            Map<String, Object> equipmentEntity = (Map<String, Object>) ctx.channel().attr(AttributeKey.valueOf("scannerInfo")).get();
            System.out.println("连接断开: " + equipmentEntity.get("lightIp").toString());
            ctx.channel().eventLoop().schedule(() -> connect(equipmentEntity), RECONNECT_DELAY, TimeUnit.SECONDS);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            cause.printStackTrace();
            ctx.close();
        }
    }

    public Channel getChannelByIp(String stashName, String stashType) {
        return channelMap.get(stashName + ":" + stashType);
    }

    public void sendCommand(String stashName, String stashType, String command) {
        Channel channel = getChannelByIp(stashName, stashType);
        if (channel != null && channel.isActive()) {
            byte[] bytes = hexStringToByteArray(command);

            // 通过Netty发送字节数组
            channel.writeAndFlush(channel.alloc().buffer().writeBytes(bytes));
            System.out.println("指令发送成功: " + command + " 给设备: " + stashName + stashType);
        } else {
            System.err.println("设备未连接: " + stashName + stashType);
        }
    }

    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
}
