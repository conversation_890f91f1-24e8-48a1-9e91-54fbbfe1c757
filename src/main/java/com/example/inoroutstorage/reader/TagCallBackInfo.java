package com.example.inoroutstorage.reader;

import com.example.inoroutstorage.main.MainMaxController;
import com.rfid.CReader;
import com.rfid.ReadTag;
import com.rfid.TagCallback;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName Test * @Description TODO
 * <AUTHOR>
 * @Date 17:37 2024/6/6
 * @Version 1.0
 **/

public class TagCallBackInfo {

    @Value("${reader.tagIp}")
    private String tagIp;

    @Value("${reader.tagProt}")
    private int tagProt;

    public CReader Connect() {
        CReader cReader = new CReader(tagIp, tagProt, 4, 0);
        return cReader;
    }

}
