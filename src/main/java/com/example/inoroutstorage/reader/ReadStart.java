package com.example.inoroutstorage.reader;

import com.example.inoroutstorage.netty.MultiErrorClient;
import com.example.inoroutstorage.netty.ThreadSend;
import com.rfid.CReader;
import com.rfid.ReaderParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ReadStart * @Description TODO
 * <AUTHOR>
 * @Date 13:20 2024/10/24
 * @Version 1.0
 **/
@Component
public class ReadStart implements ApplicationRunner {

    public static boolean READER_STATUS = false;
    public static CReader CREADER = null;

    @Value("${reader.tagIp}")
    private String tagIp;

    @Value("${reader.tagProt}")
    private int tagProt;

    @Value("${light.tagIp}")
    private String lightIp;

    @Value("${light.tagProt}")
    private int lightProt;
    public static MultiErrorClient clientError = new MultiErrorClient();

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        ReaderParameter readerParameter = new ReaderParameter();
//        readerParameter.SetAntenna();
        Map<String, Object> map = new HashMap<>();
        map.put("lightIp", lightIp);
        map.put("lightProt", lightProt);
        clientError.addScanner(map);
        clientError.start();


        ThreadSend threadSend = new ThreadSend(lightIp, lightProt+"", "off");
        threadSend.start();

        CReader cReader = new CReader(tagIp, tagProt, 8, 0);
        ReaderParameter readerParameter = new ReaderParameter();
        readerParameter.SetSession(2);
        cReader.SetInventoryParameter(readerParameter);
        int isConnect = cReader.Connect();
        if (isConnect == 0) {
            READER_STATUS = true;
            CREADER = cReader;
        }
    }
}
