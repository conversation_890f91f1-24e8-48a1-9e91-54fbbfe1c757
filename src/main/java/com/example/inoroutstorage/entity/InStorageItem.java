package com.example.inoroutstorage.entity;

import lombok.Data;

/**
 * 借用归还子表
 */
@Data
public class InStorageItem  {

    private String recordNumber; //记录编号关联主表
    private Long assetsId;

    private String assetsNumber; //器材编号
    private String assetsName; //器材名称
    private String assetsSpecifications;//规格型号
    private Long assetsUnit; //器材单位ID
    private String assetsUnitName; //器材单位
    private String assetsCode; //器材条码
    private String assetsRfid; //器材RFID
    private String assetsTypeName; //器材类型
    private Long assetsType; //器材类型
    private String assetsPosition; //常用货位



    private Integer inStorageCount; //入库数量

    private Integer countScan; //已扫数量
    private Double inStoragePrice; //入库单价



}
