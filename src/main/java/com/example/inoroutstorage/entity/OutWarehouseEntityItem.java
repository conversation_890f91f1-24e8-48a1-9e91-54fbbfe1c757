package com.example.inoroutstorage.entity;

import lombok.Data;


@Data
public class OutWarehouseEntityItem  {
    private String recordNumber; //记录编号
    private String assetsNumber; //器材编号
    private String assetsName; //器材名称
    private String assetsSpecifications;//规格型号
    private Long assetsUnit; //器材单位ID
    private String assetsUnitName; //器材单位
    private String assetsCode; //器材条码
    private String assetsRfid; //器材RFID
    private String assetsTypeName; //器材类型
    private Long assetsType; //器材类型
    private String assetsPosition; //常用货位
    private Integer consumableNum;//数量
    private Long assetsId;
    private Integer nowRepertory; //当前库存
    private String warehouseInfo; //库存仓库
    private String areaInfo; //库位
    private String locationInfo; //库位
    private String operationNumber; //操作单号
    private Double price;//单价
    private Integer countScan; //已扫数量
}
