package com.example.inoroutstorage.entity;

import lombok.Data;

/**
 * 器材总览
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/26 9:33
 */
@Data
public class TagInfo  {

    private String assetsNumber; //器材编号
    private String assetsName; //器材名称
    private String assetsSpecifications;//规格型号
    private Long assetsUnit; //器材单位ID
    private String assetsUnitName; //器材单位
    private String assetsCode; //器材条码
    private String assetsRfid; //器材RFID
    private String assetsTypeName; //器材类型
    private String assetsTypeNameSuperior; //器材类型
    private Long assetsType; //器材类型
    private String assetsPosition; //常用货位
    private String warehouseInfo; //库存仓库
    private String areaInfo; //库位
    private int assetsCount; //数量
    private int countScan; //数量
    // 0-未用 1-在库 2-出库 3-借出 4-维修
    private String assetsStatus; //状态

    private String tagNumber; //系统标签编号

    private Long serialNumber; //流水号
}
