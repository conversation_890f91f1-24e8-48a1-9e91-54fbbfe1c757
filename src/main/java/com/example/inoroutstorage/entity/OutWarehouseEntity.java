package com.example.inoroutstorage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
public class OutWarehouseEntity  {
    private String outReson; //记录原由
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date outDate;//出库时间
    private String recordNumber; //记录编号
    private String assetsName; //资产名称
    private String assetsSpecifications; //规格型号
    private String assetsBrand; //资产品牌
    private String assetsUnit; //单位
    private Integer nowRepertory; //当前库存
    private Integer total;//出库数量
    private String people;//出库人
    private String consumablePrice;//单价
    private String consumableCompany;//往来单位
    private String consumableDepartment;//部门

    private Integer countScan; //已扫数量
    private Integer status; //状态

    private String itemJson;
}
