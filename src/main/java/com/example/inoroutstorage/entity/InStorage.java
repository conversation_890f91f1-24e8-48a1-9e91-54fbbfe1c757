package com.example.inoroutstorage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InStorage {
    private String recordNumber; //记录编号
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inStorageDate;//入库时间
    private String inStorageReson; //记录原由
    private String people;//入库人

    private String inStorageWarehouse;//收入仓库
    private String inStorageArea;//收入库区
    private String inStorageLocation;//收入库位

    private Integer total; //入库总数

    private Integer countScan; //已扫数量
    private Integer status; //状态
    /**
     * 入库子表json
     */
    private String itemJson;


}
