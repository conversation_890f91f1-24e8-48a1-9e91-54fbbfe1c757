package com.example.inoroutstorage.main;

import com.example.inoroutstorage.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MainController * @Description TODO
 * <AUTHOR>
 * @Date 11:03 2024/5/28
 * @Version 1.0
 **/
@Controller
public class MainController {
    protected static final Logger LOGGER = LoggerFactory.getLogger(MainController.class);

    private String urlPrefix = "main";

    @RequestMapping("/login")
    public String login(ModelMap mmap) {
        return urlPrefix + "/login";
    }

    @RequestMapping
    public String index(ModelMap mmap) {
        return urlPrefix + "/index";
    }


    public static String QUEST_NO = null;
    public static String ORDER_NO = null;


    public static List<TagInfo> tagInfoList = new ArrayList<>();
//
//
//    @RequestMapping("/uploadRfid")
//    @ResponseBody
//    private ResponseData uploadRfid(String rfid) {
//        MainService mainService = new MainService();
//        ResponseData responseData1 = mainService.uploadRfid(rfid);
//        if(responseData1.get("code").equals("50001")){
//            errMsg = responseData1.get("msg").toString();
//            error();
//        }
//        return ResponseData.success();
//    }


    @RequestMapping("/getData")
    @ResponseBody
    public ResponseData getData(String operation, String orderInfo) {
        QUEST_NO = operation;
        ORDER_NO = orderInfo;
//        if (StringUtils.isNotEmpty(errMsg) && readStatis == false) {
//            String msg = errMsg;
//            errMsg = "";
//            tagList.clear();
//            return ResponseData.error(msg);
//        }
        return ResponseData.success(tagInfoList);
    }

    @RequestMapping("/order-selection")
    public String orderSelection(ModelMap mmap, String operation) {
        mmap.put("operation", operation);
        return urlPrefix + "/order-selection";
    }


    @RequestMapping("/getItem")
    @ResponseBody
    public List<TagInfo> getItem(String operation, String orderInfo) {
        List<TagInfo> l = new ArrayList<>();

        switch (operation) {
            case "inbound"://入库
//                List<InStorageItem> inStorageItemList = inStorageItemService.findByRecordNumber(orderInfo);
//                for (InStorageItem inStorageItem : inStorageItemList) {
//                    TagInfo tagInfo = new TagInfo();
//                    BeanUtils.copyProperties(inStorageItem, tagInfo);
//                    tagInfo.setAssetsCount(inStorageItem.getInStorageCount());
//                    l.add(tagInfo);
//                }
                break;
            case "outbound"://出库
//                List<OutWarehouseEntityItem> outWarehouseEntityItems = outWarehouseItemService.findByRecordNumber(orderInfo);
//                for (OutWarehouseEntityItem outWarehouseEntityItem : outWarehouseEntityItems) {
//                    TagInfo tagInfo = new TagInfo();
//                    BeanUtils.copyProperties(outWarehouseEntityItem, tagInfo);
//                    tagInfo.setAssetsCount(outWarehouseEntityItem.getConsumableNum());
//                    l.add(tagInfo);
//                }
                break;
        }
        return l;
    }

    //
//    @RequestMapping("/confirmScan")
//    @ResponseBody
//    public List<TagInfo> confirmScan(String operation, String orderInfo, String scannedData) {
//        tagList.clear();
//        tagInfoList.clear();
//        if (SerialBeanInfo.sendStatus == false) {
//            Thread tsend = new SerialBeanInfo.MyThread(2000L);
//            tsend.start();
//        }
////        SerialBeanInfo.setMessage(SerialBeanInfo.OK_MSG_CODE);
//
//        QUEST_NO = null;
//        ORDER_NO = null;
//        switch (operation) {
//            case "inbound"://入库
//                inStorageService.confirmScan(orderInfo, scannedData);
//                break;
//            case "outbound"://出库
//                outWarehouseService.confirmScan(orderInfo, scannedData);
////                mmap.put("title", "器材出库");
//                break;
//
//        }
//
//
//        List<InStorageItem> inStorageItemList = inStorageItemService.findByRecordNumber(orderInfo);
//        List<TagInfo> l = new ArrayList<>();
//        for (InStorageItem inStorageItem : inStorageItemList) {
//            TagInfo tagInfo = new TagInfo();
//            BeanUtils.copyProperties(inStorageItem, tagInfo);
//            tagInfo.setAssetsCount(inStorageItem.getInStorageCount());
//            if (operation.equals("repair-return") || operation.equals("return")) {
//                tagInfo.setWarehouseInfo(null);
//                tagInfo.setAreaInfo(null);
//            }
//            l.add(tagInfo);
//        }
//        return l;
//    }
    @RequestMapping("/blindReceipt")
    public String blindReceipt(ModelMap mmap, String operation, String orderId) {
        mmap.put("operation", operation);
        switch (operation) {
            case "inbound"://入库
//                InStorage inStorage = inStorageService.findByRecordNumber(orderId);
                mmap.put("orderInfo", "");
                mmap.put("title", "器材入库");
//                mmap.put("orderInfoOriginal", inStorageItemService.findByRecordNumber(orderId));
                break;
            case "outbound"://出库
//                OutWarehouseEntity outWarehouseEntity = outWarehouseService.findByRecordNumber(orderId);
                mmap.put("orderInfo", "");
//                mmap.put("orderInfoOriginal", outWarehouseItemService.findByRecordNumber(orderId));
                mmap.put("title", "器材出库");
                break;

        }
        return urlPrefix + "/blindReceipt";
    }

    @RequestMapping("/storage-entry")
    public String storageEntry(ModelMap mmap, String operation, String orderId) {
        mmap.put("operation", operation);
        switch (operation) {
            case "inbound"://入库
//                InStorage inStorage = inStorageService.findByRecordNumber(orderId);
                mmap.put("orderInfo", "");
                mmap.put("title", "器材入库");
//                mmap.put("orderInfoOriginal", inStorageItemService.findByRecordNumber(orderId));
                break;
            case "outbound"://出库
//                OutWarehouseEntity outWarehouseEntity = outWarehouseService.findByRecordNumber(orderId);
                mmap.put("orderInfo", "");
//                mmap.put("orderInfoOriginal", outWarehouseItemService.findByRecordNumber(orderId));
                mmap.put("title", "器材出库");
                break;

        }
        return urlPrefix + "/storageEntry";
    }


    @RequestMapping("/index")
    public String index() {

        return urlPrefix + "/index";
    }

    @RequestMapping("/getOrder")
    @ResponseBody
    private ResponseData getOrder(MainCriteria mainCriteria) {
        switch (mainCriteria.getOperation()) {
            case "inbound"://入库
                return ResponseData.success();
            case "outbound"://出库
                return ResponseData.success();
        }
        return ResponseData.error("请传输正确类型");
    }


}
