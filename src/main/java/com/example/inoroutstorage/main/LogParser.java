package com.example.inoroutstorage.main;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LogParser {


    public static void main(String[] args) {
        // 文件路径
        String filePath = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/1359952b98e24ea5c8a1ff2c026cf6b9/Message/MessageTemp/9e20f478899dc29eb19741386f9343c8/File/logs/fast.log"; // 替换为你的日志文件路径

        // 调用读取文件并解析内容的方法
        readAndParseFile(filePath);
    }

    private static void readAndParseFile(String filePath) {
        BufferedReader reader = null;
        List<String> list = new ArrayList<>();
        try {
            // 创建BufferedReader读取文件
            reader = new BufferedReader(new FileReader(filePath));
            String line;

            // 更新后的正则表达式匹配labelId和modelCode
            String regex = "\"labelId\":\"([^\"]+)\".*\"modelCode\":\"(SL1000P)\"";
            Pattern pattern = Pattern.compile(regex);

            // 逐行读取文件
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    // 如果匹配到labelId和modelCode，输出labelId
                    String labelId = matcher.group(1);
                    list.add(labelId);
                    System.out.println( labelId);
                }
            }
            System.out.println(list.size());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}