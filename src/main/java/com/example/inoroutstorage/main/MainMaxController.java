package com.example.inoroutstorage.main;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.inoroutstorage.apiInfo.ApiInfoData;
import com.example.inoroutstorage.bean.*;
import com.example.inoroutstorage.entity.MainCriteria;
import com.example.inoroutstorage.entity.ResponseData;
import com.example.inoroutstorage.netty.ThreadSend;
import com.rfid.CReader;
import com.rfid.ReadTag;
import com.rfid.TagCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

/**
 * @ClassName MainController * @Description TODO
 * <AUTHOR>
 * @Date 11:03 2024/5/28
 * @Version 1.0
 **/
@Controller
@RequestMapping("/mainMax")
public class MainMaxController implements TagCallback {
    protected static final Logger LOGGER = LoggerFactory.getLogger(MainMaxController.class);


    @Value("${reader.tagIp}")
    private String tagIp;

    @Value("${reader.tagProt}")
    private int tagProt;

    @Value("${light.tagIp}")
    private String lightIp;

    @Value("${light.tagProt}")
    private int lightProt;


    public static CReader cReader = null;

    public static String token = "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyQ29udGV4dCI6IntcInVzZXJuYW1lXCI6XCJhZG1pblwiLFwibmlja05hbWVcIjpcIui2hee6p-euoeeQhuWRmFwiLFwiZmFjZVwiOlwiXCIsXCJpZFwiOlwiMTMzNzMwNjExMDI3NzQ3NjM1MlwiLFwibG9uZ1Rlcm1cIjpmYWxzZSxcInJvbGVcIjpcIk1BTkFHRVJcIixcImlzU3VwZXJcIjp0cnVlfSIsInN1YiI6ImFkbWluIiwiZXhwIjoxNzM0NTUwMDg4fQ.msFQe0K-eefwgQfHoDeXaizd3yYzrlJzNtVfkLnrMe0";

    @RequestMapping("/login")
    @ResponseBody
    private ResponseData login(String userName, String password) {
        String md5Hex = DigestUtil.md5Hex(password);
        try {
            HttpRequest post = HttpUtil.createPost(ApiInfoData.LOGIN_URL + "?username=" + userName + "&password=" + md5Hex);
            HttpResponse response = post.execute();
            int status = response.getStatus();
            if (status == 200) {
                JSONObject jsonObject = JSONObject.parseObject(response.body());
                String result = jsonObject.getString("result");
                JSONObject jsonObject1 = JSONObject.parseObject(result);
                String accessToken = jsonObject1.getString("accessToken");
                token = accessToken;
                return ResponseData.success("登录成功", token);
            } else {
                return ResponseData.error("账号或密码错误");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("服务器连接失败");
        }
    }


    @RequestMapping("/confirmScan")
    @ResponseBody
    public ResponseData confirmScan(String operation, String orderInfo, String scannedData) {
        List<JSONObject> jsonArray = JSONArray.parseArray(scannedData, JSONObject.class);

        List<String> uploadList = new ArrayList<>();

        for (JSONObject jsonObject : jsonArray) {
            String labelIdString = jsonObject.getString("labelIdList");
            List<String> list = Arrays.asList(labelIdString.split(","));
            uploadList.addAll(list);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("orderSn", orderInfo);
        data.put("labelIdList", uploadList);

        String type = "";
        HttpRequest post = null;
        switch (operation) {
            case "inbound"://入库
                type = "入库";
                post = HttpRequest.post(ApiInfoData.RECEIVED_URL);
                break;
            case "outbound"://出库
                type = "出库";

                post = HttpRequest.post(ApiInfoData.DELIVERY_URL);
                break;
        }
        post.body(JSONObject.toJSONString(data));
        post.header("accessToken", token);
        HttpResponse response = post.execute();
        String body = response.body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        Integer code = jsonObject.getInteger("code");
        String message = jsonObject.getString("message");
        stopRead();
        assetList.clear();
        tagList.clear();
        if (success) {
            return ResponseData.success(message);
        } else {
            if (code == 403) {
                return ResponseData.error("403", "登录过期！");
            } else {

                ThreadSend threadSend = new ThreadSend(lightIp, lightProt + "", "on");
                threadSend.start();
                return ResponseData.error(message);
            }
        }

//        int status = response.getStatus();
//        if (status == 200) {
//        } else if (status == 403) {
//            return ResponseData.error("403", "登录过期！");
//        } else {
//            String body = response.body();
//        }
//        return ResponseData.success();
    }


    @RequestMapping("/submitData")
    @ResponseBody
    public ResponseData submitData(String operation, String orderInfo, String scannedData) {
        JSONObject jsonObjectnew = JSONObject.parseObject(scannedData, JSONObject.class);

        List<String> uploadList = new ArrayList<>();

        String labelIdString = jsonObjectnew.getString("labelId");
        List<String> list = Arrays.asList(labelIdString.split(","));
        uploadList.addAll(list);
        Map<String, Object> data = new HashMap<>();
        data.put("orderSn", orderInfo);
        data.put("labelIdList", uploadList);

        String type = "";
        HttpRequest post = null;
        switch (operation) {
            case "inbound"://入库
                type = "入库";
                post = HttpRequest.post(ApiInfoData.RECEIVED_URL);
                break;
            case "outbound"://出库
                type = "出库";

                post = HttpRequest.post(ApiInfoData.DELIVERY_URL);
                break;
        }
        post.body(JSONObject.toJSONString(data));
        post.header("accessToken", token);
        HttpResponse response = post.execute();
        String body = response.body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        Integer code = jsonObject.getInteger("code");
        String message = jsonObject.getString("message");
//        stopRead();
        delData(scannedData);
        if (success) {
            return ResponseData.success(message);
        } else {
            if (code == 403) {
                return ResponseData.error("403", "登录过期！");
            } else {

                ThreadSend threadSend = new ThreadSend(lightIp, lightProt + "", "on");
                threadSend.start();
                return ResponseData.error(message);
            }
        }

//        int status = response.getStatus();
//        if (status == 200) {
//        } else if (status == 403) {
//            return ResponseData.error("403", "登录过期！");
//        } else {
//            String body = response.body();
//        }
//        return ResponseData.success();
    }


    List<String> selectTagList = new ArrayList<>();
    private static List<Asset> assetList = new ArrayList<>();
    private static List<OrderItem> orderList = new ArrayList<>();


    public static List<Asset> mergeGoods(List<Asset> goodsList) {
        Map<String, Asset> mergedMap = new HashMap<>();

        for (Asset goods : goodsList) {
            String goodsId = goods.getSkuId();
            String labelId = goods.getLabelId();

            if (!mergedMap.containsKey(goodsId)) {
                goods.setCountInfo(1); // 初始计数
                mergedMap.put(goodsId, goods);
            } else {
                Asset existingGoods = mergedMap.get(goodsId);
                int countInfo = existingGoods.getCountInfo();
                existingGoods.setCountInfo(countInfo + 1);

                // 判断 labelId 是否重复
                String existingLabelIds = existingGoods.getLabelId();
                if (!existingLabelIds.contains(labelId)) {
                    existingGoods.setLabelId(existingLabelIds + "," + labelId);
                }
            }
        }
        return new ArrayList<>(mergedMap.values());
    }
    List<Asset> assetListError = new ArrayList<>();


    @RequestMapping("/getData")
    @ResponseBody
    public ResponseData getData() {
//
//        for (String tag : tagList) {
//            if (selectTagList.contains(tag)) {
//                continue;
//            }
//            System.out.println("增加标签"+tag);
//            selectTagList.add(tag);
//            HttpRequest post = HttpUtil.createPost(ApiInfoData.LIST_ASSETS + "?labelId=" + tag);
//            post.header("accessToken", token);
//            HttpResponse response = post.execute();
//            int status = response.getStatus();
//            if (status == 200) {
//                System.out.println("111111111111111111");
//                JSONObject apiResponse = JSONObject.parseObject(response.body());
//                List<Asset> result1 = JSONArray.parseArray(apiResponse.getString("result"), Asset.class);
//                assetList.addAll(result1);
//                assetList = mergeGoods(assetList);
//            }else{
//                selectTagList.remove(tag);
//            }
//        }
        // 创建一个 Set 来存储 orderList 中的 goodsId
        Set<String> orderGoodsIds = new HashSet<>();

        for (OrderItem orderItem : orderList) {
            orderGoodsIds.add(orderItem.getSkuId());
        }
        boolean errorStatus = false;
        // 使用 Iterator 遍历 assetList 并移除不在 orderGoodsIds 中的元素
        Iterator<Asset> iterator = assetList.iterator();
        while (iterator.hasNext()) {
            Asset asset = iterator.next();
            if (!orderGoodsIds.contains(asset.getSkuId())) {
                iterator.remove(); // 移除不在 orderList 中的 asset
                assetListError.add(asset);
                errorStatus = true;
            } else {
                for (OrderItem orderItem : orderList) {
                    if (asset.getSkuId().equals(orderItem.getSkuId())) {
                        asset.setGoodsNum(orderItem.getGoodsNum());
                    }
                }
            }
        }
        newTagCount = assetList.size();
        if (errorStatus) {
            ThreadSend threadSend = new ThreadSend(lightIp, lightProt + "", "on");
            threadSend.start();
            errorStatus = false;
//            StringBuffer stringBuffer = new StringBuffer();
            String result = assetListError.stream()
                    .map(Asset::getOutCode)
                    .collect(Collectors.joining(","));
            return ResponseData.error("存在异常设备" + result);
        } else {
            return ResponseData.success(mergeGoods(assetList));
        }
    }

    @RequestMapping("/getCount")
    @ResponseBody
    public ResponseData getCount() {
        // 判断 newTagCount 是否变化
        if (newTagCount == lastTagCount) {
            // 如果新增标签数量没有变化，返回一个不同的状态
            return ResponseData.error("标签数量未变化");
        } else {
            // 更新 lastTagCount 为当前的 newTagCount
            lastTagCount = newTagCount;
            // 返回新增标签数量和资产列表
            return ResponseData.success(newTagCount);
        }
    }

    boolean statusRead = true;

    @RequestMapping("/startRead")
    @ResponseBody
    private ResponseData startRead() {
        cReader = new CReader(tagIp, tagProt, 8, 0);
        int isConnect = cReader.Connect();
        if (isConnect == 0) {
            cReader.SetCallBack(new MainMaxController());
            int i = cReader.StartRead();
            if (i == 0) {
                statusRead = true;
                return ResponseData.success("开启成功");
            } else {
                return ResponseData.error("开启失败");
            }
        } else {
            return ResponseData.error("开启失败");
        }
    }

    @RequestMapping("/cleanData")
    @ResponseBody
    private ResponseData cleanData() {
        stopRead();
        tagList.clear();
        assetList.clear();
        newTagCount = 0;
        lastTagCount = 0;
        return ResponseData.success();
    }

    @RequestMapping("/errorOk")
    @ResponseBody
    private ResponseData errorOk() {
        assetListError.clear();
        ThreadSend threadSend = new ThreadSend(lightIp, lightProt + "", "off");
        threadSend.start();
        return ResponseData.success("开启成功");
    }

    @RequestMapping("/stopRead")
    @ResponseBody
    private ResponseData stopRead() {
        statusRead = false;
        Thread thread = new Thread(() -> {
            cReader.StopRead();
            cReader.DisConnect();
        });
        thread.start();
        LockSupport.unpark(thread);

//        assetList.clear();
//        tagList.clear();
        return ResponseData.success();
    }

    public static class stopThread extends Thread {
        private CReader creader;

        public stopThread(CReader creader) {
            this.creader = creader;
        }

        @Override
        public void run() {
            creader.StopRead();
        }
    }

    @RequestMapping("/getOrderInfo")
    @ResponseBody
    private ResponseData getOrderInfo(String orderId) {
        orderList.clear();
        HttpRequest post = HttpUtil.createPost(ApiInfoData.ORDER_INFO + "/" + orderId);
        orderIdData = orderId;
        post.header("accessToken", token);
        HttpResponse response = post.execute();
        int status = response.getStatus();
        if (status == 200) {
            OrderResponse apiResponse = JSONObject.parseObject(response.body(), OrderResponse.class);
            OrderResult result = apiResponse.getResult();
            orderList.addAll(result.getOrderItemList());
            return ResponseData.success(apiResponse.getResult());
        } else if (status == 403) {
            return ResponseData.error("403", "登录过期！");
        }

        return ResponseData.error("状态：" + status + "服务器异常:");
//            ApiResponse apiResponse = JSONObject.parseObject(response.body(), ApiResponse.class);
    }

    @RequestMapping("/delData")
    @ResponseBody
    private ResponseData delData(String data) {
        // 解析 OrderItem 对象
        OrderItem orderItem = JSONObject.parseObject(data, OrderItem.class);

        // 优化 assetList 的删除操作，避免在遍历时修改集合
        Iterator<Asset> assetIterator = assetList.iterator();
        while (assetIterator.hasNext()) {
            Asset asset = assetIterator.next();
            if (asset.getSkuId().equals(orderItem.getSkuId())) {
                assetIterator.remove(); // 使用 Iterator 的 remove 方法安全移除元素
            }
        }

        // 优化 tagList 的删除操作
        Set<String> rfidsToRemove = new HashSet<>(Arrays.asList(orderItem.getLabelId().split(",")));
        tagList.removeAll(rfidsToRemove); // 批量删除，提高效率

        // 打印数据以便调试
        System.out.println(data);

        // 返回成功响应
        return ResponseData.success();
    }


    Map<String, String> map = new HashMap<>();

    @RequestMapping("/getOrderInfoBlindReceipt")
    @ResponseBody
    private ResponseData getOrderInfoBlindReceipt(MainCriteria mainCriteria) {
        HttpRequest post = HttpUtil.createPost(ApiInfoData.LIST_ORDER_V2 + "?orderStatus=RETURNED&pageSize=" + 1 + "&pageNumber=1");
        post.header("accessToken", token);
        HttpResponse response = post.execute();
        int status = response.getStatus();
        if (status == 200) {
            OrderResponseList apiResponse = JSONObject.parseObject(response.body(), OrderResponseList.class);
            for (OrderResult orderResult : apiResponse.getResult()) {
                List<String> epc = new ArrayList<>();
                for (OrderItem orderItem : orderResult.getOrderItemList()) {
                    for (ReturnGoods unReturngoodsAsset : orderItem.getUnReturngoodsAssets()) {
                        map.put(unReturngoodsAsset.getLabelId(), orderResult.getOrder().getOrderSn());
                    }
                }
            }
            return ResponseData.success(apiResponse.getResult());
        } else if (status == 403) {
            return ResponseData.error("403", "登录过期！");
        }

        return ResponseData.error("状态：" + status + "服务器异常:");
//            ApiResponse apiResponse = JSONObject.parseObject(response.body(), ApiResponse.class);
    }

    @RequestMapping("/getOrder")
    @ResponseBody
    private ResponseData getOrder(MainCriteria mainCriteria) {
        HttpRequest post = null;

        switch (mainCriteria.getOperation()) {
            case "inbound"://入库
                post = HttpUtil.createPost(ApiInfoData.LIST_ORDER + "?orderStatus=RETURNED&pageSize=" + mainCriteria.getPageSize() + "&pageNumber=" + mainCriteria.getPageNumber() + "&searchKey=" + mainCriteria.getSearchKey());
                break;
            case "outbound"://出库
                post = HttpUtil.createPost(ApiInfoData.LIST_ORDER + "?orderStatus=UNDELIVERED&pageSize=" + mainCriteria.getPageSize() + "&pageNumber=" + mainCriteria.getPageNumber() + "&searchKey=" + mainCriteria.getSearchKey());
                break;
        }
        post.header("accessToken", token);
        HttpResponse response = post.execute();
        int status = response.getStatus();
        if (status == 200) {
//            String data = "{\n" +
//                    "    \"success\": true,\n" +
//                    "    \"message\": \"success\",\n" +
//                    "    \"code\": 200,\n" +
//                    "    \"timestamp\": 1729667725721,\n" +
//                    "    \"result\": {\n" +
//                    "        \"records\": [\n" +
//                    "            {\n" +
//                    "                \"orderSn\": \"O202410231848986391615258625\",\n" +
//                    "                \"programName\": \"测试2\",\n" +
//                    "                \"programDept\": \"测试2\",\n" +
//                    "                \"memberName\": \"jack\",\n" +
//                    "                \"beginTime\": \"2024-10-27 00:00:00\",\n" +
//                    "                \"endTime\": \"2024-10-29 00:00:00\",\n" +
//                    "                \"goodsNum\": \"2\",\n" +
//                    "                \"createTime\": \"2024-10-23 15:14:42.981000\",\n" +
//                    "                \"remark\": \"\"\n" +
//                    "            },\n" +
//                    "            {\n" +
//                    "                \"orderSn\": \"O202410231848986256034381825\",\n" +
//                    "                \"programName\": \"12\",\n" +
//                    "                \"programDept\": \"12\",\n" +
//                    "                \"memberName\": \"jack\",\n" +
//                    "                \"beginTime\": \"2024-10-27 00:00:00\",\n" +
//                    "                \"endTime\": \"2024-10-29 00:00:00\",\n" +
//                    "                \"goodsNum\": \"2\",\n" +
//                    "                \"createTime\": \"2024-10-23 15:14:10.986000\",\n" +
//                    "                \"remark\": \"\"\n" +
//                    "            }\n" +
//                    "        ],\n" +
//                    "        \"total\": 2,\n" +
//                    "        \"size\": 10,\n" +
//                    "        \"current\": 1,\n" +
//                    "        \"orders\": [],\n" +
//                    "        \"optimizeCountSql\": true,\n" +
//                    "        \"searchCount\": true,\n" +
//                    "        \"countId\": null,\n" +
//                    "        \"maxLimit\": null,\n" +
//                    "        \"pages\": 1\n" +
//                    "    }\n" +
//                    "}";
//            ApiResponse apiResponse = JSONObject.parseObject(data, ApiResponse.class);
            ApiResponse apiResponse = JSONObject.parseObject(response.body(), ApiResponse.class);
            return ResponseData.success(apiResponse.getResult());
        } else if (status == 403) {
            return ResponseData.error("403", "登录过期！");
        }
        return null;
//        return ResponseData.error("请传输正确类型");
    }

    public static List<String> tagList = new ArrayList();

    private static int newTagCount = 0;  // 当前新增标签数量
    private static int lastTagCount = 0;  // 上次新增标签数量，用于比较
    private static String orderIdData;  // 上次新增标签数量，用于比较

    @Override
    public void tagCallback(ReadTag readTag) {
        if (!statusRead) {
            return;
        }

        if (tagList.contains(readTag.epcId)) {
            return;
        }

        // 使用新的线程来进行标签查询，避免阻塞主线程
        new Thread(() -> {
            synchronized (tagList) {
                tagList.add(readTag.epcId);
            }

            HttpRequest post = HttpUtil.createPost(ApiInfoData.LIST_ASSETS + "?labelId=" + readTag.epcId);
            post.header("accessToken", token);
            System.out.println("发送请求");

            HttpResponse response = post.execute();
            int status = response.getStatus();
            System.out.println("返回请求");
            LOGGER.info("订单---" + orderIdData + "---读取标签-----" + readTag.epcId + "-----------返回请求" + response.body());
            if (status == 200) {
                System.out.println("请求成功，处理返回数据");
                JSONObject apiResponse = JSONObject.parseObject(response.body());
                List<Asset> result1 = JSONArray.parseArray(apiResponse.getString("result"), Asset.class);
                synchronized (assetList) {  // 确保线程安全地修改 assetList
                    assetList.addAll(result1);
                    newTagCount++;  // 记录新增标签数量
                }
            } else {
                synchronized (tagList) {  // 确保线程安全地修改 tagList
                    tagList.remove(readTag.epcId);
//                    newTagCount--;  // 如果请求失败，减少新增标签计数
                }
            }
        }).start();
    }

    @Override
    public void StopReadCallback() {
//        Map<String, Thread> map = MainController.threadMap;
//        for (String key : map.keySet()) {
//            Thread thread = MainController.threadMap.get(key);
//            thread.interrupt();
//        }
//        tagList.clear();
    }
}
