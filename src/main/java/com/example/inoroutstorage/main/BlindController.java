package com.example.inoroutstorage.main;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.locks.LockSupport;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.inoroutstorage.apiInfo.ApiInfoData;
import com.example.inoroutstorage.entity.ResponseData;
import com.example.inoroutstorage.netty.ThreadSend;
import com.rfid.CReader;
import com.rfid.ReadTag;
import com.rfid.TagCallback;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

@Controller
@RequestMapping("/blind")
public class BlindController implements TagCallback {

	@Value("${reader.tagIp}")
	private String tagIp;

	@Value("${reader.tagProt}")
	private int tagProt;

	@Value("${light.tagIp}")
	private String lightIp;

	@Value("${light.tagProt}")
	private int lightProt;

	public static CReader cReader = null;
	boolean statusRead = true;

	public static List<String> tagList = new ArrayList();

	@RequestMapping("/getBlindData")
	@ResponseBody
	public ResponseData getBlindData() {
		return ResponseData.success(tagList);
	}

	@RequestMapping("/stopRead")
	@ResponseBody
	private ResponseData stopRead() {
		statusRead = false;
		Thread thread = new Thread(()->{
			if(cReader != null) {
				cReader.StopRead();
				cReader.DisConnect();
			}
			tagList.clear();
		});
		thread.start();
		LockSupport.unpark(thread);
		return ResponseData.success();
	}

	@RequestMapping("/startRead")
	@ResponseBody
	private ResponseData startRead() {
		cReader = new CReader(tagIp, tagProt, 8, 0);
		int isConnect = cReader.Connect();
		if (isConnect == 0) {
			cReader.SetCallBack(new BlindController());
			int i = cReader.StartRead();
			if (i == 0) {
				statusRead = true;
				return ResponseData.success("开启成功");
			} else {
				return ResponseData.error("开启失败");
			}
		} else {
			return ResponseData.error("开启失败");
		}
	}

	@RequestMapping("/confirmScan")
	@ResponseBody
	public ResponseData confirmScan(String scannedData) {
		tagList.clear();
		List<JSONObject> jsonArray = JSONArray.parseArray(scannedData, JSONObject.class);
		Map<String, List<String>> orderTag = new HashMap<String, List<String>>();
		
		for (JSONObject jsonObject : jsonArray) {
			String labelIdString = jsonObject.getString("labelIdList");
			List<String> list = Arrays.asList(labelIdString.split(","));
			String orderSn = jsonObject.getString("orderSn");
			List<String> tagList = orderTag.get(orderSn);
			if(tagList == null) {
				tagList = new ArrayList<String>();
				orderTag.put(orderSn, tagList);
			}
			tagList.addAll(list);
		}
		
		for(Entry<String, List<String>> entry: orderTag.entrySet()) {
			Map<String, Object> data = new HashMap<>();
			data.put("orderSn", entry.getKey());
			data.put("labelIdList", entry.getValue());
			
			HttpRequest post = null;
			post = HttpRequest.post(ApiInfoData.RECEIVED_URL);
			
			System.out.println(JSONObject.toJSONString(data));
			post.body(JSONObject.toJSONString(data));
			
			post.header("accessToken", MainMaxController.token);
			HttpResponse response = post.execute();
			String body = response.body();
			
			JSONObject jsonObject = JSONObject.parseObject(body);
			Boolean success = jsonObject.getBoolean("success");
			Integer code = jsonObject.getInteger("code");
			String message = jsonObject.getString("message");
			if (!success) {
				if (code == 403) {
					return ResponseData.error("403", "登录过期！");
				} else {
					ThreadSend threadSend = new ThreadSend(lightIp, lightProt + "", "on");
					threadSend.start();
					return ResponseData.error(message);
				}
			}
		}
		return ResponseData.success();
	}

	@Override
	public void StopReadCallback() {
		tagList.clear();
	}

	@Override
	public void tagCallback(ReadTag readTag) {
		System.out.println(readTag.epcId);
		if (!statusRead) {
			return;
		}

		if (tagList.contains(readTag.epcId)) {
			return;
		}
		tagList.add(readTag.epcId);
	}
}
