/**
 * 消息提示组件
 * 用于在大屏幕上显示操作反馈信息
 */

class MessageToast {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        // 创建消息容器
        this.container = document.createElement('div');
        this.container.id = 'message-toast-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `message-toast message-toast-${type}`;
        
        // 根据类型设置图标
        let icon = '';
        switch(type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i>';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle"></i>';
                break;
            case 'info':
            default:
                icon = '<i class="fas fa-info-circle"></i>';
                break;
        }

        toast.innerHTML = `
            <div class="message-toast-content">
                <div class="message-toast-icon">${icon}</div>
                <div class="message-toast-text">${message}</div>
            </div>
        `;

        // 添加样式
        toast.style.cssText = `
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            min-width: 300px;
            max-width: 500px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-left: 4px solid ${this.getTypeColor(type)};
            transform: translateX(100%);
            transition: all 0.3s ease;
            pointer-events: auto;
            font-size: 16px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        // 内容样式
        const style = document.createElement('style');
        style.textContent = `
            .message-toast-content {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            .message-toast-icon {
                font-size: 20px;
                color: ${this.getTypeColor(type)};
                flex-shrink: 0;
            }
            .message-toast-text {
                flex: 1;
                line-height: 1.4;
            }
        `;
        document.head.appendChild(style);

        this.container.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            this.hide(toast);
        }, duration);

        // 点击隐藏
        toast.addEventListener('click', () => {
            this.hide(toast);
        });

        return toast;
    }

    hide(toast) {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    getTypeColor(type) {
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#FF9800',
            info: '#2196F3'
        };
        return colors[type] || colors.info;
    }

    // 便捷方法
    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// 创建全局实例
window.messageToast = new MessageToast();

// 全局便捷方法
window.showMessage = function(message, type, duration) {
    return window.messageToast.show(message, type, duration);
};

window.showSuccess = function(message, duration) {
    return window.messageToast.success(message, duration);
};

window.showError = function(message, duration) {
    return window.messageToast.error(message, duration);
};

window.showWarning = function(message, duration) {
    return window.messageToast.warning(message, duration);
};

window.showInfo = function(message, duration) {
    return window.messageToast.info(message, duration);
};
