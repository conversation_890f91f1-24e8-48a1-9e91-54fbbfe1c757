<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<configuration>-->
<!--    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>-->

<!--    <springProperty scope="context" name="LOG_PATH" source="logging.file.path" defaultValue="logs/"/>-->
<!--    <springProperty scope="context" name="LOG_FILE" source="logging.file.name" defaultValue="mis"/>-->

<!--    &lt;!&ndash; 日志记录器，日期滚动记录 &ndash;&gt;-->
<!--    <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <file>${LOG_PATH}${LOG_FILE}.log</file>-->

<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${LOG_PATH}total/${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>10MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--            <maxHistory>180</maxHistory> &lt;!&ndash; 保留天数 &ndash;&gt;-->
<!--        </rollingPolicy>-->

<!--        <append>true</append>-->

<!--        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">-->
<!--            <pattern>%date [%level] [%thread] %logger{60} [%file : %line] %msg%n</pattern>-->
<!--            <charset>utf-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->

<!--    <root level="info">-->
<!--        <appender-ref ref="FILE_ALL"/>-->
<!--    </root>-->
<!--</configuration>-->
