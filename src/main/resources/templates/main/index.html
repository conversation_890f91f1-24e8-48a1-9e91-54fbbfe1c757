<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出入库管理系统 - 大屏展示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            overflow: hidden;
            color: white;
            user-select: none;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px 40px;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 48px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 24px;
            opacity: 0.8;
            font-weight: 300;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 140px);
            padding: 30px;
            gap: 30px;
        }

        .module-card {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .module-card:hover::before {
            left: 100%;
        }

        .module-icon {
            font-size: 120px;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        }

        .inbound .module-icon {
            color: #4CAF50;
        }

        .outbound .module-icon {
            color: #FF9800;
        }

        .inventory .module-icon {
            color: #2196F3;
        }

        .module-title {
            font-size: 36px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .module-description {
            font-size: 18px;
            text-align: center;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .module-status {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #666;
            position: relative;
        }

        .status-indicator.active {
            background: #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
        }

        .status-indicator.active::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(76, 175, 80, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
            }
        }

        .time-display {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-container {
                flex-direction: column;
                padding: 20px;
                gap: 20px;
            }

            .module-card {
                padding: 30px;
            }

            .module-icon {
                font-size: 80px;
            }

            .module-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>出入库管理系统</h1>
        <div class="subtitle">Storage Management System</div>
        <div class="time-display" id="currentTime"></div>
    </div>

    <div class="main-container">
        <!-- 入库模块 -->
        <div class="module-card inbound" onclick="navigateToModule('inbound')">
            <div class="module-icon">
                <i class="fas fa-arrow-down"></i>
            </div>
            <div class="module-title">入库管理</div>
            <div class="module-description">
                器材入库操作<br>
                扫描识别 · 数据录入 · 库存更新
            </div>
            <div class="module-status">
                <div class="status-indicator" id="inbound-status"></div>
                <span style="font-size: 14px; margin-left: 10px;">系统状态</span>
            </div>
        </div>

        <!-- 出库模块 -->
        <div class="module-card outbound" onclick="navigateToModule('outbound')">
            <div class="module-icon">
                <i class="fas fa-arrow-up"></i>
            </div>
            <div class="module-title">出库管理</div>
            <div class="module-description">
                器材出库操作<br>
                订单处理 · 扫描确认 · 库存扣减
            </div>
            <div class="module-status">
                <div class="status-indicator" id="outbound-status"></div>
                <span style="font-size: 14px; margin-left: 10px;">系统状态</span>
            </div>
        </div>

        <!-- 盘点模块 -->
        <div class="module-card inventory" onclick="navigateToModule('inventory')">
            <div class="module-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="module-title">库存盘点</div>
            <div class="module-description">
                库存盘点操作<br>
                全面扫描 · 数据对比 · 差异分析
            </div>
            <div class="module-status">
                <div class="status-indicator" id="inventory-status"></div>
                <span style="font-size: 14px; margin-left: 10px;">系统状态</span>
            </div>
        </div>
    </div>

    <script src="/js/jquery.min.js"></script>
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 导航到不同模块
        function navigateToModule(module) {
            switch(module) {
                case 'inbound':
                    window.location.href = '/inbound-management';
                    break;
                case 'outbound':
                    window.location.href = '/outbound-management';
                    break;
                case 'inventory':
                    window.location.href = '/inventory-main';
                    break;
            }
        }

        // 检查系统状态
        function checkSystemStatus() {
            // 这里可以调用后端接口检查各模块状态
            // 示例：模拟状态检查
            const modules = ['inbound', 'outbound', 'inventory'];
            modules.forEach(module => {
                const statusElement = document.getElementById(module + '-status');
                // 这里应该调用实际的状态检查接口
                // 暂时设置为活跃状态作为示例
                statusElement.classList.add('active', 'pulse');
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            checkSystemStatus();

            // 定期检查系统状态
            setInterval(checkSystemStatus, 30000);
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case '1':
                    navigateToModule('inbound');
                    break;
                case '2':
                    navigateToModule('outbound');
                    break;
                case '3':
                    navigateToModule('inventory');
                    break;
            }
        });
    </script>
</body>
</html>