<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="/css/all.min.css" rel="stylesheet">
    <link href="/css/sweetalert2.min.css" rel="stylesheet">
    <!-- 在页面头部引入 SweetAlert2 -->
    <title id="title"></title>
    <style>body {
        font-family: Arial, sans-serif;
        background: linear-gradient(to bottom, #2c3e50, #34495e);
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        color: white;
    }

    .container {
        background-color: #1e1e1e;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        max-width: 90vw;
    }

    /* 订单信息样式 */
    .order-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background-color: #2c3e50;
        padding: 10px;
        border-radius: 10px;
    }

    .order-info div {
        flex: 1;
        margin: 5px;
        padding: 10px;
        background-color: #34495e;
        border-radius: 5px;
        min-width: 15em;
        text-align: center;
        font-size: 12px; /* 字体适配 */
    }

    /* 表格容器 */
    .table-container {
        margin-top: 20px;
        max-height: 400px;
        overflow-y: auto;
        border-radius: 10px;
        background-color: #2c3e50;
        padding: 10px;
        position: relative;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }

    /* 表格样式 */
    table {
        width: 100%;
        border-collapse: collapse;
        color: white;
    }

    th, td {
        padding: 8px; /* 字体适配 */
        text-align: left;
        border-bottom: 1px solid #34495e;
    }

    th {
        background-color: #2980b9;
        position: sticky; /* 固定表头 */
        top: 0;
        z-index: 10;
        font-size: 12px;
        box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.3);
    }

    td {
        background-color: #2c3e50;
        font-size: 12px; /* 内容字体缩小 */
    }

    tbody tr:hover {
        background-color: #1abc9c; /* 行悬浮高亮 */
        color: #000;
    }

    /* 滚动条样式优化 */
    .table-container::-webkit-scrollbar {
        width: 8px;
    }

    .table-container::-webkit-scrollbar-thumb {
        background-color: #3498db;
        border-radius: 4px;
    }

    .table-container::-webkit-scrollbar-track {
        background-color: #2c3e50;
    }

    /* 按钮样式 */
    .button-container {
        text-align: center;
        margin-top: 20px;
    }

    button {
        background-color: #2980b9;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        font-size: 12px; /* 字体适配 */
        transition: background-color 0.3s;
    }

    button:hover {
        background-color: #3498db;
    }

    button.return-button {
        background-color: #e74c3c;
    }

    button.return-button:hover {
        background-color: #c0392b;
    }

    /* 弹窗样式 */
    .error-popup, .success-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        text-align: center;
        font-size: 14px;
    }
    /* 弹窗样式 */
    .popup-table {
        max-width: 90%; /* 限制最大宽度，防止过宽 */
        max-height: 60vh; /* 限制最大高度，防止超出屏幕 */
        overflow-y: auto; /* 超出时滚动 */
        width: -webkit-fill-available;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        text-align: center;
        font-size: 14px;
    }
    .error-popup {
        background-color: #e74c3c;
    }

    .success-popup {
        background-color: #27ae60;
    }

    .popup-button {
        background-color: #1e8449;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        color: white;
        margin-top: 10px;
        transition: background-color 0.3s;
    }

    .popup-button:hover {
        background-color: #145a32;
    }
    /* 灯泡默认状态 */
    #lightbulb-icon {
        font-size: 24px;
        transition: color 0.3s ease; /* 为颜色变化添加过渡效果 */
    }

    /* 灯泡亮起时的样式 */
    #lightbulb-icon.on {
        color: yellow; /* 亮起时变为黄色 */
    }

    /* sentOutCode 弹框样式 */
    #sentOutCode-popup {
        background-color: #34495e;
        color: white;
        max-width: 90vw;
        text-align: center;
    }

    #sentOutCode-popup ul {
        list-style: none;
        padding: 0;
        margin: 10px 0;
    }

    #sentOutCode-popup ul li {
        background-color: #2c3e50;
        padding: 10px;
        margin: 5px 0;
        border-radius: 5px;
        text-align: left;
    }

    /* 触摸优化 - 按钮 */
    .downBtn {
        background-color: #2980b9;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        font-size: 12px; /* 字体适配 */
        transition: background-color 0.3s;
    }
    .subBtn{
        background-color: #2980b9;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        font-size: 12px; /* 字体适配 */
        transition: background-color 0.3s;
    }
    .subBtn del{
        background-color: #e74c3c;
        color: white;
    }
    /* 操作列按钮容器 */
    .td-actions {
        display: flex;
        gap: 8px;
        flex-wrap: nowrap;
    }

    /* 自定义确认弹窗 */
    .custom-confirm-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #2c3e50;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 0 15px rgba(0,0,0,0.3);
        z-index: 1001;
        width: 80%;
        max-width: 300px;
        display: none;
    }

    .confirm-buttons {
        margin-top: 15px;
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .confirm-button {
        flex: 1;
        padding: 12px;
        border: none;
        border-radius: 4px;
        font-size: 16px;
    }

    .confirm-yes { background: #27ae60; color: white; }
    .confirm-no { background: #e74c3c; color: white; }
    </style>
</head>
<body>
<div class="container">
    <button type="button" class="return-button" onclick="goBack()">返回</button>
    <i id="lightbulb-icon" class="fa fa-lightbulb" style="color: gray;"></i>

    <div class="order-info">
        <div>订单号: <span id="orderSn"></span></div>
        <div>节目名称: <span id="programName"></span></div>
        <div>节目部门: <span id="programDept"></span></div>
        <div>下单人: <span id="memberName"></span></div>
        <div>租借开始时间: <span id="beginTime"></span></div>
        <div>租借结束时间: <span id="endTime"></span></div>
        <div>设备数量: <span id="goodsNum"></span></div>
        <div>创建时间: <span id="createTime"></span></div>
        <div>备注: <span id="remark"></span></div>
    </div>
    <h3 style="padding-bottom: 5px;">单据器材信息 总数 <span id="oldCount">0</span></h3>
    <div class="table-container">
        <table id="original-data-table">
            <thead>
            <tr>
                <th data-field="index">序号</th>
                <th data-field="goodsId">设备ID</th>
                <!--                <th data-field="category">分类</th>-->
                <th data-field="goodsName">设备名称</th>
                <th data-field="skuId">外编号</th>
                <th data-field="goodsNum">设备总数</th>
                <th data-field="unsentNum">未提交数量</th>
                <th data-field="beginTime">租借开始时间</th>
                <th data-field="endTime">租借结束时间</th>
            </tr>
            </thead>
            <tbody>
            <!-- 动态数据将插入到这里 -->
            </tbody>
        </table>
    </div>
    <h3 style="padding-bottom: 5px;">已扫器材信息 总数 <span id="newCount">0</span></h3>
    <div class="table-container">
        <table id="scanned-data-table">
            <thead>
            <tr>


                <th data-field="index">序号</th>
                <th data-field="goodsId">设备ID</th>
                <!--                <th data-field="category">分类</th>-->
                <th data-field="goodsName">设备名称</th>
                <th data-field="skuId">外编号</th>
                <th data-field="countInfo">扫描数量</th>
                <th style="display: none" data-field="labelId">标签信息</th>
                <th  data-field="czxx">操作</th>


            </tr>
            </thead>
            <tbody>
            <!-- 动态数据将插入到这里 -->
            </tbody>
        </table>
    </div>
    <div class="button-container">
        <button type="button" class="downBtn" id="startButton" onclick="startScan()">开始扫描</button>
        <button type="button" class="downBtn"id="stopButton" onclick="stopScan()">停止扫描</button>
        <button type="button"class="downBtn"  id="confirmButton" onclick="confirmScan()">提交数据</button>
    </div>
</div>
<div id="error-popup" class="error-popup">
    <p id="error-message"></p>
    <button onclick="closeError()">确认</button>
</div>

<div id="success-popup" class="success-popup">
    <p id="success-message"></p>
    <button onclick="closeSuccess()">确认</button>
</div>
<!-- 自定义确认弹窗 -->
<div id="custom-confirm" class="custom-confirm-popup">
    <p id="confirm-message"></p>
    <div class="confirm-buttons">
        <button id="confirm-yes" class="confirm-button confirm-yes">确定</button>
        <button id="confirm-no" class="confirm-button confirm-no">取消</button>
    </div>
</div>
<div id="sentOutCode-popup" class="popup-table">
    <h3>外编号信息</h3>
    <ul id="sentOutCode-list"></ul>
    <button onclick="closeSentOutCodePopup()">关闭</button>
</div>
<script src="/js/jquery.min.js"></script>
<script src="/js/<EMAIL>"></script>
<script>
    let scanning = false;
    let scanInterval;
    let scanIntervalGetCount;
    let scannedItems = [];

    function goBack() {
        window.history.back();
        $.post('mainMax/cleanData', null, function (data) {

        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });
    }

    // 获取URL中的orderId参数
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('orderId');
    const operation = urlParams.get('operation');


    //---------------------弹框---------------

    // 显示弹框方法
    function showSentOutCodePopup(sentOutCodeList) {
        const sentOutCodePopup = document.getElementById('sentOutCode-popup');
        const sentOutCodeListElement = document.getElementById('sentOutCode-list');
        sentOutCodeListElement.innerHTML = ''; // 清空之前的列表内容

        if (Array.isArray(sentOutCodeList) && sentOutCodeList.length > 0) {
            sentOutCodeList.forEach(code => {
                const listItem = document.createElement('li');
                listItem.textContent = code; // 填充后端返回的值
                sentOutCodeListElement.appendChild(listItem);
            });
            sentOutCodePopup.style.display = 'block'; // 显示弹框
        } else {
            showError("未找到相关信息");
        }
    }

    // 关闭弹框方法
    function closeSentOutCodePopup() {
        const sentOutCodePopup = document.getElementById('sentOutCode-popup');
        sentOutCodePopup.style.display = 'none'; // 隐藏弹框
    }







    function playVoice(text) {
        const msg = new SpeechSynthesisUtterance(text);
        msg.lang = 'zh-CN'; // 设置为中文
        window.speechSynthesis.speak(msg);
    }

    // 页面加载时获取订单信息
    $(document).ready(function () {
        if (orderId) {
            fetchOrderInfo(orderId);
        } else {
            showError("未找到订单ID");
        }
        if (operation == "inbound") {
            $("#title").text("入库操作");
        } else {
            $("#title").text("出库操作");
        }
    });

    // 根据orderId从后台获取订单信息
    function fetchOrderInfo(orderId) {
        $.ajax({
            url: 'mainMax/getOrderInfo', // 替换为你实际的接口地址
            type: 'GET',
            data: {orderId: orderId},
            success: function (response) {
                if (response.code === "00000") {
                    const orderInfo = response.data.order;
                    const orderInfoList = response.data.orderItemList;
                    // 更新页面中的订单信息
                    $('#orderSn').text(orderInfo.orderSn);
                    $('#programName').text(orderInfo.programName);
                    $('#programDept').text(orderInfo.programDept);
                    $('#memberName').text(orderInfo.memberName);
                    $('#beginTime').text(orderInfo.beginTime);
                    $('#endTime').text(orderInfo.endTime);
                    $('#goodsNum').text(orderInfo.goodsNum);
                    $('#createTime').text(orderInfo.createTime);
                    $('#remark').text(orderInfo.remark || '');
                    $("#oldCount").text(orderInfoList.length);
                    addDataToTable(orderInfoList, 'original-data-table');

                } else {
                    if (response.code == '403') {
                        window.location.href = "login";
                    } else {
                        showError("订单信息获取失败: " + response.msg);
                    }
                }
            },
            error: function (error) {
                console.error('Error fetching order info:', error);
                showError("订单信息获取失败");
            }
        });
    }

    var readStatus = false;
    const lightbulbIcon = document.getElementById('lightbulb-icon');

    function startScan() {
        if (!readStatus) {
            lightbulbIcon.classList.add('on');
            lightbulbIcon.style.color = 'yellow'; // 你也可以直接设置颜色
            readStatus = true;
            playVoice("开始扫描");
            if (!scanning) {
                const params = {
                    orderInfo: orderId,
                    operation: operation
                };
                $.post('mainMax/startRead', params, function (data) {
                    if (data.code == '00000') {
                        showSuccess("开启成功")
                        scanning = true;
                        scanInterval = setInterval(simulateScan, 1000);
                        scanIntervalGetCount = setInterval(getCount, 5000);
                    } else {
                        showError(data.msg);
                    }
                }, "json")
                    .fail(function (error) {
                        console.error('Error fetching orders:', error);
                    });
            }
        }

    }

    function stopScan() {
        readStatus = false;
        lightbulbIcon.classList.remove('on');
        lightbulbIcon.style.color = 'gray'; // 恢复默认颜色
        // if (scanning) {
        const params = {
            orderInfo: $("#recordNumber").text(),
            operation: operation
        };
        $.post('mainMax/stopRead', params, function (data) {
            if (data.code == "00000") {
                showSuccess("关闭成功");
                scanning = false;
                clearInterval(scanInterval);
                clearInterval(scanIntervalGetCount);
            } else {
                showError(data.msg);
            }
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });

        // }
    }

    var countInfo = 0;

    function getCount() {
        $.post("mainMax/getCount", null, function (datas) {
            if (datas.code == "00000") {
                if(datas.data!=0){
                    playVoice("扫描成功数量为" + datas.data);
                }
            }
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });
    }


    function simulateScan() {
        const params = {
            orderInfo: orderId,
            operation: operation
        };
        $.post("mainMax/getData", params, function (datas) {
            if (datas.code == "00000") {
                var data = datas.data;
                $("#newCount").text(data.length);
                var countInfoData = 0;
                data.forEach(function (da) {
                    if (!scannedItems.some(item => isEquivalent(item, da))) {
                        scannedItems.push(da);
                    }
                    countInfoData += da.countInfo;
                });
                if (countInfo != countInfoData) {

                }
                scanning = false;
                addDataToTable(data, 'scanned-data-table');
                checkSatisfaction('original-data-table');
            } else {
                // console.log(datas.msg
                //     alert(datas.msg)
                showError(datas.msg);
                // opt.modal.msgError(datas.msg);
            }
            // Also check the original table satisfaction
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });
    }

    function checkSatisfaction(tableId) {
        const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
        for (let i = 0; i < tableBody.rows.length; i++) {
            const row = tableBody.rows[i];
            const requiredCount = parseInt(row.cells[5].textContent);
            const scannedCount = parseInt(row.cells[7]?.textContent); // Adjust based on original or scanned table
            if (requiredCount === scannedCount) {
                row.classList.add('satisfied');
            } else {
                row.classList.remove('satisfied');
            }
        }
    }

    function isEquivalent(a, b) {
        if (a.assetsRfid == b.assetsRfid) {
            return true;
        } else {
            return false;
        }
        // 自定义对象比较逻辑
    }


    function addDataToTable(orderItemList, tableId) {
        const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
        tableBody.innerHTML = ''; // 清空已有表格内容

        orderItemList.forEach(function (item, index) {
            let row = tableBody.insertRow();
            // 为每个单元格动态填充数据
            const headers = document.getElementById(tableId).getElementsByTagName('thead')[0].getElementsByTagName('th');
            // 循环表头的每一列
            for (let i = 0; i < headers.length; i++) {
                const fieldName = headers[i].getAttribute('data-field');
                let cell = row.insertCell(i);
                if (fieldName === "labelId") {
                    cell.style.display = 'none';
                }
                if (fieldName === "index") {
                    cell.textContent = index + 1; // 序号
                } else if (fieldName === "czxx") {
                    // 删除按钮
                    let deleteButton = document.createElement("button");
                    deleteButton.textContent = "删除";
                    deleteButton.classList.add("subBtn","del");

                    deleteButton.onclick = async function () {
                        const { isConfirmed } = await Swal.fire({
                            title: '确认删除？',
                            text: "删除设备："+item.goodsName+"数量："+item.countInfo,
                            icon: 'warning',
                            showCancelButton: true,
                            allowOutsideClick: false,  // 禁止点击外部关闭
                            allowEscapeKey: false,     // 禁止ESC键关闭
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: '确认删除',
                            cancelButtonText: '取消',
                            customClass: {
                                popup: 'custom-swal-popup',  // 添加自定义类名
                                confirmButton: 'btn btn-danger',
                                cancelButton: 'btn btn-secondary'
                            }
                        });
                        if (isConfirmed) {
                            $.post('mainMax/delData',
                                {
                                    data: JSON.stringify(item),
                                }, function (response) {
                                    if (response.code == "00000") {
                                        showSuccess("删除成功");
                                        simulateScan();
                                    } else {
                                        showError(response.msg);
                                    }
                                }, 'json')
                                .fail(function (error) {
                                    console.error('删除失败:', error);
                                    showError("删除失败");
                                });
                        }
                    };




                    // deleteButton.onclick = function () {
                    //     if (confirm('确定要删除此项吗？')) {
                    //         $.post('mainMax/delData',
                    //             {
                    //                 data: JSON.stringify(item),
                    //             }, function (response) {
                    //                 if (response.code == "00000") {
                    //                     showSuccess("删除成功");
                    //                     simulateScan();
                    //                 } else {
                    //                     showError(response.msg);
                    //                 }
                    //             }, 'json')
                    //             .fail(function (error) {
                    //                 console.error('删除失败:', error);
                    //                 showError("删除失败");
                    //             });
                    //     }
                    // };
                    cell.appendChild(deleteButton);

                    // 提交按钮
                    let submitButton = document.createElement("button");
                    submitButton.textContent = "提交";
                    submitButton.classList.add("btn", "btn-sm", "btn-success");
                    submitButton.onclick = async function () {
                        const { isConfirmed } = await Swal.fire({
                            title: '确认提交？',
                            text: "提交设备："+item.goodsName+"数量："+item.countInfo,
                            icon: 'warning',
                            allowOutsideClick: false,  // 禁止点击外部关闭
                            allowEscapeKey: false,     // 禁止ESC键关闭
                            showCancelButton: true,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: '确认提交',
                            cancelButtonText: '取消',
                            customClass: {
                                popup: 'custom-swal-popup',  // 添加自定义类名
                                confirmButton: 'btn btn-danger',
                                cancelButton: 'btn btn-secondary'
                            }
                        });
                        if (isConfirmed) {
                            $.post('mainMax/submitData',
                                {
                                    scannedData: JSON.stringify(item),
                                    orderInfo: orderId,
                                    operation: operation
                                }, function (response) {
                                    if (response.code == "00000") {
                                        fetchOrderInfo(orderId);
                                        showSuccess("确认成功");
                                    } else {
                                        showError(response.msg);
                                    }
                                    console.log('Confirmation response:', response);
                                    scannedItems = [];
                                    // loadOriginalData();
                                    checkSatisfaction('original-data-table');
                                    clearScannedTable();// Also check the original table satisfaction
                                }, 'json')
                                .fail(function (error) {
                                    console.error('Error confirming scan:', error);
                                    showError("确认失败");

                                });
                        }
                    };


                    // submitButton.onclick = function () {
                    //     if (confirm('确定要提交此项吗？')) {
                    //         $.post('mainMax/submitData',
                    //             {
                    //                 data: JSON.stringify(item),
                    //             }, function (response) {
                    //                 if (response.code === "00000") {
                    //                     showSuccess("提交成功");
                    //                     simulateScan();
                    //                 } else {
                    //                     showError(response.msg);
                    //                 }
                    //             }, 'json')
                    //             .fail(function (error) {
                    //                 console.error('提交失败:', error);
                    //                 showError("提交失败");
                    //             });
                    //     }
                    // };
                    cell.appendChild(submitButton);
                } else if (item.hasOwnProperty(fieldName)) {
                    cell.textContent = item[fieldName]; // 其他数据字段
                } else {
                    cell.textContent = ''; // 如果没有对应字段，清空
                }
            }

            row.style.cursor = 'pointer';
            if (tableId === "original-data-table") {
                row.addEventListener('click', function () {
                    showSentOutCodePopup(item.sentOutCodeList);
                });
            }
            if (item.unsentNum == 0) {
                row.style.color = '#55d948';
            }
        });
    }



    function clearScannedTable() {
        const tableBody = document.getElementById('scanned-data-table').getElementsByTagName('tbody')[0];
        tableBody.innerHTML = ''; // Clear the scanned table rows
        $("#newCount").text('0'); // Reset the new count
    }

    function confirmScan() {

        // stopScan();
        const tableBody = document.getElementById('scanned-data-table').getElementsByTagName('tbody')[0];
        const scannedData = [];
        for (let i = 0; i < tableBody.rows.length; i++) {
            const row = tableBody.rows[i];
            console.log(row);
            const rowData = {
                labelIdList: row.cells[5].textContent,
            };
            scannedData.push(rowData);
        }


        if (scannedData.length > 0) {
            $.post('mainMax/confirmScan',
                {
                    scannedData: JSON.stringify(scannedData),
                    orderInfo: orderId,
                    operation: operation
                }, function (response) {
                    if (response.code == "00000") {
                        fetchOrderInfo(orderId);
                        showSuccess("确认成功");
                    } else {
                        showError(response.msg);
                    }
                    console.log('Confirmation response:', response);
                    scannedItems = [];
                    // loadOriginalData();
                    checkSatisfaction('original-data-table');
                    clearScannedTable();// Also check the original table satisfaction
                }, 'json')
                .fail(function (error) {
                    console.error('Error confirming scan:', error);
                    showError("确认失败");

                });
        } else {
            showError("没有数据提交失败");

        }


    }

    async function showError(message) {


        const { isConfirmed } = await Swal.fire({
            title: '错误提醒',
            text: message,
            icon: 'error',
            showCancelButton: false,
            allowOutsideClick: false,  // 禁止点击外部关闭
            allowEscapeKey: false,     // 禁止ESC键关闭
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '关闭',
            // cancelButtonText: '取消',
            customClass: {
                popup: 'custom-swal-popup',  // 添加自定义类名
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            }
        });
        if (isConfirmed) {
            $.post('mainMax/errorOk',
                {}, function (response) {

                }, 'json')
                .fail(function (error) {
                    console.error('Error confirming scan:', error);
                    showError("确认失败");

                });
        }











        //
        // const errorPopup = document.getElementById('error-popup');
        // const errorMessage = document.getElementById('error-message');
        // errorMessage.textContent = message;
        // errorPopup.style.display = 'block';
    }

    function closeError() {
        const errorPopup = document.getElementById('error-popup');
        errorPopup.style.display = 'none';

        $.post('mainMax/errorOk',
            {}, function (response) {

            }, 'json')
            .fail(function (error) {
                console.error('Error confirming scan:', error);
                showError("确认失败");

            });

    }

    async function showSuccess(message) {


        const { isConfirmed } = await Swal.fire({
            // type: 'warning', // 弹框类型
            title: '系统提醒',
            text: message,
            icon: 'success',
            allowOutsideClick: false,  // 禁止点击外部关闭
            allowEscapeKey: false,     // 禁止ESC键关闭
            showCancelButton: false,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '关闭',
            // cancelButtonText: '取消',
        });






        //
        // const successPopup = document.getElementById('success-popup');
        // const successMessage = document.getElementById('success-message');
        // successMessage.textContent = message;
        // successPopup.style.display = 'block';
    }

    function closeSuccess() {
        const successPopup = document.getElementById('success-popup');
        successPopup.style.display = 'none';
    }

    // Load original data on page load
    document.addEventListener('DOMContentLoaded', function () {


        // opt.modal.error("123132")
        // layer.alert("信息提示", {icon: 6});
        // loadOriginalData();
        // scanInterval = setInterval(simulateScan, 1000);

    });
</script>
</body>
</html>
