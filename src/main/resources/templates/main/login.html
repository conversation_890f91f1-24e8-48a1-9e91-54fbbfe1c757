<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>登录 - 出入库管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom, #2c3e50, #34495e);
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
        }
        .login-container {
            background-color: #2c3e50;
            width: 90%;
            max-width: 500px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.7);
        }
        h2 {
            margin-bottom: 20px;
            font-size: 24px;
        }
        input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: none;
            border-radius: 5px;
            background-color: #3498db;
            color: white;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        input:focus {
            background-color: #2980b9;
            outline: none;
        }
        button {
            padding: 12px;
            width: 100%;
            border: none;
            border-radius: 5px;
            background-color: #27ae60;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #219653;
        }
        .error-popup, .success-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            text-align: center;
        }

        .error-popup {
            background-color: #e74c3c;
        }

        .error-popup button {
            background-color: #c0392b;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            color: white;
            transition: background-color 0.3s;
            width: 300px;
        }

        .error-popup button:hover {
            background-color: #a5281a;
        }

        .success-popup {
            background-color: #27ae60;
        }

        .success-popup button {
            background-color: #1e8449;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            color: white;
            transition: background-color 0.3s;
            width: 300px;
        }

        .success-popup button:hover {
            background-color: #145a32;
        }
    </style>
</head>

<body>
<div class="login-container">
    <h2>登录</h2>
    <input type="text" id="username" placeholder="用户名" value="admin" required>
    <input type="password" id="password" placeholder="密码" value="Symantec120!" required>
    <button onclick="login()">登录</button>
    <div id="error-popup" class="error-popup">
        <p id="error-message"></p >
        <button onclick="closeError()">确认</button>
    </div>

    <div id="success-popup" class="success-popup">
        <p id="success-message"></p >
        <button onclick="closeSuccess()">确认</button>
    </div>
</div>
<script src="/js/jquery.min.js"></script>
<script>
    function login() {
        var username = document.getElementById('username').value;
        var password = document.getElementById('password').value;
        var errorMessage = document.getElementById('error-message');
        const params = {
            userName: username,
            password: "Symantec120!"
            // password: password
        };
        $.post('mainMax/login', params, function(data) {
           if(data.code=='00000'){
               window.location.href = '/index'; // 登录成功后跳转到主页
           }else{
               showError(data.msg);
           }
        }, "json")
            .fail(function(error) {
                console.error('Error fetching orders:', error);
            });

        // // 模拟登录验证
        // if (username === 'admin' && password === 'password') { // 替换为实际验证逻辑
        //     window.location.href = '/home'; // 登录成功后跳转到主页
        // } else {
        //     errorMessage.textContent = '用户名或密码错误！';
        // }
    }
    function showError(message) {
        const errorPopup = document.getElementById('error-popup');
        const errorMessage = document.getElementById('error-message');
        errorMessage.textContent = message;
        errorPopup.style.display = 'block';
    }

    function closeError() {
        const errorPopup = document.getElementById('error-popup');
        errorPopup.style.display = 'none';
    }

    function showSuccess(message) {
        const successPopup = document.getElementById('success-popup');
        const successMessage = document.getElementById('success-message');
        successMessage.textContent = message;
        successPopup.style.display = 'block';
    }

    function closeSuccess() {
        const successPopup = document.getElementById('success-popup');
        successPopup.style.display = 'none';
    }
</script>
</body>
