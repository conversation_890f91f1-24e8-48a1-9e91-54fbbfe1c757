<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存盘点 - 出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
            user-select: none;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 36px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .return-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .return-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-container {
            padding: 30px;
            height: calc(100vh - 100px);
            overflow-y: auto;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .stat-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .stat-card .number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-card .label {
            font-size: 16px;
            opacity: 0.8;
        }

        .inventory-table-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .table-title {
            font-size: 24px;
            font-weight: bold;
        }

        .search-container {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
            min-width: 300px;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-button {
            padding: 12px 25px;
            background: #4CAF50;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .inventory-table th,
        .inventory-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .inventory-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
            font-size: 16px;
        }

        .inventory-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-normal {
            background: #4CAF50;
            color: white;
        }

        .status-shortage {
            background: #FF9800;
            color: white;
        }

        .status-excess {
            background: #2196F3;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .pagination button.active {
            background: #4CAF50;
        }

        .time-display {
            font-size: 18px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-clipboard-list"></i> 库存盘点</h1>
        <div class="time-display" id="currentTime"></div>
        <button class="return-button" onclick="goBack()">
            <i class="fas fa-arrow-left"></i> 返回主页
        </button>
    </div>

    <div class="main-container">
        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="icon" style="color: #4CAF50;">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="number" id="totalItems">0</div>
                <div class="label">总库存数量</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #FF9800;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="number" id="shortageItems">0</div>
                <div class="label">缺货商品</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #2196F3;">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="number" id="excessItems">0</div>
                <div class="label">超量商品</div>
            </div>
            <div class="stat-card">
                <div class="icon" style="color: #9C27B0;">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="number" id="lastInventoryDate">--</div>
                <div class="label">上次盘点</div>
            </div>
        </div>

        <!-- 库存表格 -->
        <div class="inventory-table-container">
            <div class="table-header">
                <div class="table-title">库存明细</div>
                <div class="search-container">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索商品名称、编号...">
                    <button class="search-button" onclick="searchInventory()">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>

            <table class="inventory-table" id="inventoryTable">
                <thead>
                    <tr>
                        <th>商品编号</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th>当前库存</th>
                        <th>标准库存</th>
                        <th>差异</th>
                        <th>状态</th>
                        <th>最后更新</th>
                    </tr>
                </thead>
                <tbody id="inventoryTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>

            <div class="pagination" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script src="/js/jquery.min.js"></script>
    <script>
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 返回主页
        function goBack() {
            window.location.href = '/index';
        }

        // 加载库存数据
        function loadInventoryData() {
            // 这里应该调用后端接口获取库存数据
            // 示例数据
            const mockData = {
                totalItems: 1250,
                shortageItems: 15,
                excessItems: 8,
                lastInventoryDate: '2024-01-10',
                inventoryList: [
                    {
                        code: 'EQ001',
                        name: '摄像机A',
                        category: '摄影设备',
                        currentStock: 8,
                        standardStock: 10,
                        difference: -2,
                        status: 'shortage',
                        lastUpdate: '2024-01-15 14:30'
                    },
                    {
                        code: 'EQ002',
                        name: '三脚架B',
                        category: '支撑设备',
                        currentStock: 15,
                        standardStock: 12,
                        difference: 3,
                        status: 'excess',
                        lastUpdate: '2024-01-15 14:25'
                    }
                    // 更多数据...
                ]
            };

            updateStats(mockData);
            updateInventoryTable(mockData.inventoryList);
        }

        // 更新统计数据
        function updateStats(data) {
            document.getElementById('totalItems').textContent = data.totalItems;
            document.getElementById('shortageItems').textContent = data.shortageItems;
            document.getElementById('excessItems').textContent = data.excessItems;
            document.getElementById('lastInventoryDate').textContent = data.lastInventoryDate;
        }

        // 更新库存表格
        function updateInventoryTable(data) {
            const tbody = document.getElementById('inventoryTableBody');
            tbody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');
                const statusClass = item.status === 'shortage' ? 'status-shortage' :
                                  item.status === 'excess' ? 'status-excess' : 'status-normal';
                const statusText = item.status === 'shortage' ? '缺货' :
                                 item.status === 'excess' ? '超量' : '正常';

                row.innerHTML = `
                    <td>${item.code}</td>
                    <td>${item.name}</td>
                    <td>${item.category}</td>
                    <td>${item.currentStock}</td>
                    <td>${item.standardStock}</td>
                    <td>${item.difference > 0 ? '+' : ''}${item.difference}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${item.lastUpdate}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 搜索库存
        function searchInventory() {
            const searchTerm = document.getElementById('searchInput').value;
            // 这里应该调用后端搜索接口
            console.log('搜索:', searchTerm);
            loadInventoryData(); // 重新加载数据
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            loadInventoryData();

            // 定期刷新数据
            setInterval(loadInventoryData, 30000);
        });

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                searchInventory();
            }
        });
    </script>
</body>
</html>
