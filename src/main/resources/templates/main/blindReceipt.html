<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <title id="title"></title>
    <style>body {
        font-family: Arial, sans-serif;
        background: linear-gradient(to bottom, #2c3e50, #34495e);
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        color: white;
    }

    .container {
        background-color: #1e1e1e;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        max-width: 90vw;
        min-width: 90vw;
    }

    /* 订单信息样式 */
    .order-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background-color: #2c3e50;
        padding: 10px;
        border-radius: 10px;
    }

    .order-info div {
        flex: 1;
        margin: 5px;
        padding: 10px;
        background-color: #34495e;
        border-radius: 5px;
        min-width: 15em;
        text-align: center;
        font-size: 12px; /* 字体适配 */
    }

    /* 表格容器 */
    .table-container {
        margin-top: 20px;
        max-height: 400px;
        overflow-y: auto;
        border-radius: 10px;
        background-color: #2c3e50;
        padding: 10px;
        position: relative;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }

    /* 表格样式 */
    table {
        width: 100%;
        border-collapse: collapse;
        color: white;
    }

    th, td {
        padding: 8px; /* 字体适配 */
        text-align: left;
        border-bottom: 1px solid #34495e;
    }

    th {
        background-color: #2980b9;
        position: sticky; /* 固定表头 */
        top: 0;
        z-index: 10;
        font-size: 12px;
        box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.3);
    }

    td {
        background-color: #2c3e50;
        font-size: 12px; /* 内容字体缩小 */
    }

    tbody tr:hover {
        background-color: #1abc9c; /* 行悬浮高亮 */
        color: #000;
    }

    /* 滚动条样式优化 */
    .table-container::-webkit-scrollbar {
        width: 8px;
    }

    .table-container::-webkit-scrollbar-thumb {
        background-color: #3498db;
        border-radius: 4px;
    }

    .table-container::-webkit-scrollbar-track {
        background-color: #2c3e50;
    }

    /* 按钮样式 */
    .button-container {
        text-align: center;
        margin-top: 20px;
    }

    button {
        background-color: #2980b9;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        font-size: 12px; /* 字体适配 */
        transition: background-color 0.3s;
    }

    button:hover {
        background-color: #3498db;
    }

    button.return-button {
        background-color: #e74c3c;
    }

    button.return-button:hover {
        background-color: #c0392b;
    }

    /* 弹窗样式 */
    .error-popup, .success-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        text-align: center;
        font-size: 14px;
    }
    /* 弹窗样式 */
    .popup-table {
        max-width: 90%; /* 限制最大宽度，防止过宽 */
        max-height: 60vh; /* 限制最大高度，防止超出屏幕 */
        overflow-y: auto; /* 超出时滚动 */
        width: -webkit-fill-available;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        text-align: center;
        font-size: 14px;
    }
    .error-popup {
        background-color: #e74c3c;
    }

    .success-popup {
        background-color: #27ae60;
    }

    .popup-button {
        background-color: #1e8449;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        color: white;
        margin-top: 10px;
        transition: background-color 0.3s;
    }

    .popup-button:hover {
        background-color: #145a32;
    }
    /* 灯泡默认状态 */
    #lightbulb-icon {
        font-size: 24px;
        transition: color 0.3s ease; /* 为颜色变化添加过渡效果 */
    }

    /* 灯泡亮起时的样式 */
    #lightbulb-icon.on {
        color: yellow; /* 亮起时变为黄色 */
    }

    /* sentOutCode 弹框样式 */
    #sentOutCode-popup {
        background-color: #34495e;
        color: white;
        max-width: 90vw;
        text-align: center;
    }

    #sentOutCode-popup ul {
        list-style: none;
        padding: 0;
        margin: 10px 0;
    }

    #sentOutCode-popup ul li {
        background-color: #2c3e50;
        padding: 10px;
        margin: 5px 0;
        border-radius: 5px;
        text-align: left;
    }
    </style>
</head>
<body>
<div class="container">
    <button type="button" class="return-button" onclick="goBack()">返回</button>
    <i id="lightbulb-icon" class="fa fa-lightbulb" style="color: gray;"></i>
    <h3 style="padding-bottom: 5px;">单据器材信息 总数 <span id="oldCount">0</span></h3>
    <div class="table-container">
        <table id="original-data-table">
            <thead>
            <tr>
                <th data-field="index">序号</th>
                <th data-field="orderSn">订单号</th>
                <!--                <th data-field="category">分类</th>-->
                <th data-field="programName">节目名称</th>
                <th data-field="goodsNum">设备数量</th>
              <!--   <th data-field="">实收设备数量</th> -->
<!--                <th data-field="unsentNum">未提交数量</th>-->
                <th data-field="beginTime">租借开始时间</th>
                <th data-field="endTime">租借结束时间</th>
            </tr>
            </thead>
            <tbody>
            <!-- 动态数据将插入到这里 -->
            </tbody>
        </table>
    </div>
    <h3 style="padding-bottom: 5px;">已扫器材信息 总数 <span id="newCount">0</span></h3>
    <div class="table-container">
        <table id="scanned-data-table">
            <thead>
            <tr>
                <th data-field="index">序号</th>
                <th data-field="orderSn">订单号</th>
                <th data-field="goodsId">设备ID</th>
                <!--                <th data-field="category">分类</th>-->
                <th data-field="goodsName">设备名称</th>
                <th data-field="skuId">外编号</th>
                <th data-field="countInfo">扫描数量</th>
                <th style="display: none" data-field="labelId">标签信息</th>


            </tr>
            </thead>
            <tbody>
            <!-- 动态数据将插入到这里 -->
            </tbody>
        </table>
    </div>
    <div class="button-container">
        <button type="button" id="startButton" onclick="startScan()">开始扫描</button>
        <button type="button" id="stopButton" onclick="stopScan()">停止扫描</button>
        <button type="button" id="confirmButton" onclick="confirmScan()">提交数据</button>
    </div>
</div>
<div id="error-popup" class="error-popup">
    <p id="error-message"></p>
    <button onclick="closeError()">确认</button>
</div>

<div id="success-popup" class="success-popup">
    <p id="success-message"></p>
    <button onclick="closeSuccess()">确认</button>
</div>

<div id="sentOutCode-popup" class="popup-table">
    <h3>标签信息</h3>
    <ul id="sentOutCode-list"></ul>
    <button onclick="closeSentOutCodePopup()">关闭</button>
</div>
<script src="/js/jquery.min.js"></script>
<script>
    let scanning = false;
    let scanInterval;
    let scanIntervalGetCount;
    

    function goBack() {
    	stopScan();
        window.history.back();
    }

    // 获取URL中的orderId参数
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('orderId');
    const operation = urlParams.get('operation');


    //---------------------弹框---------------

    // 显示弹框方法
    function showSentOutCodePopup(sentOutCodeList) {
        const sentOutCodePopup = document.getElementById('sentOutCode-popup');
        const sentOutCodeListElement = document.getElementById('sentOutCode-list');
        sentOutCodeListElement.innerHTML = ''; // 清空之前的列表内容

        if (Array.isArray(sentOutCodeList) && sentOutCodeList.length > 0) {
            sentOutCodeList.forEach(code => {
                const listItem = document.createElement('li');
                listItem.textContent = code; // 填充后端返回的值
                sentOutCodeListElement.appendChild(listItem);
            });
            sentOutCodePopup.style.display = 'block'; // 显示弹框
        } else {
            showError("未找到相关信息");
        }
    }

    // 关闭弹框方法
    function closeSentOutCodePopup() {
        const sentOutCodePopup = document.getElementById('sentOutCode-popup');
        sentOutCodePopup.style.display = 'none'; // 隐藏弹框
    }







    function playVoice(text) {
        const msg = new SpeechSynthesisUtterance(text);
        msg.lang = 'zh-CN'; // 设置为中文
        window.speechSynthesis.speak(msg);
    }

    // 页面加载时获取订单信息
    $(document).ready(function () {
        if (orderId) {
            fetchOrderInfo(orderId);
        } else {
            showError("未找到订单ID");
        }
        if (operation == "inbound") {
            $("#title").text("入库操作");
        } else {
            $("#title").text("出库操作");
        }
    });
    
    var orderItemListAll = [];

    // 根据orderId从后台获取订单信息
    function fetchOrderInfo(orderId) {
        $.ajax({
            url: 'mainMax/getOrderInfoBlindReceipt',
            type: 'GET',
            data: {orderId: orderId},
            success: function (response) {
                if (response.code === "00000") {
                	var records = response.data;
                	
                	var orderList = [];
                	for(var i=0;i<records.length;i++){
                		var order = records[i].order;
                		var orderItemList = records[i].orderItemList;
                		for(var j=0;j<orderItemList.length;j++){
                			orderItemList[j].orderSn = order.orderSn;
                			orderItemListAll.push(orderItemList[j]);
                		}
                		orderList.push(order);
                	}
                	 addDataToTable(orderList, 'original-data-table');
                    // 更新页面中的订单信息
                   /*  $('#orderSn').text(orderInfo.orderSn);
                    $('#programName').text(orderInfo.programName);
                    $('#programDept').text(orderInfo.programDept);
                    $('#memberName').text(orderInfo.memberName);
                    $('#beginTime').text(orderInfo.beginTime);
                    $('#endTime').text(orderInfo.endTime);
                    $('#goodsNum').text(orderInfo.goodsNum);
                    $('#createTime').text(orderInfo.createTime);
                    $('#remark').text(orderInfo.remark || '');*/
                    $("#oldCount").text(orderList.length); 
                } else {
                    if (response.code == '403') {
                        window.location.href = "login";
                    } else {
                        showError("订单信息获取失败: " + response.msg);
                    }
                }
            },
            error: function (error) {
                console.error('Error fetching order info:', error);
                showError("订单信息获取失败");
            }
        });
    }

    var readStatus = false;
    const lightbulbIcon = document.getElementById('lightbulb-icon');

    function startScan() {
        if (!readStatus) {
            lightbulbIcon.classList.add('on');
            lightbulbIcon.style.color = 'yellow'; // 你也可以直接设置颜色
            readStatus = true;
            playVoice("开始扫描");
            if (!scanning) {
                const params = {
                    orderInfo: orderId,
                    operation: operation
                };
                $.post('blind/startRead', params, function (data) {
                    if (data.code == '00000') {
                        showSuccess("开启成功")
                        scanning = true;
                        scanInterval = setInterval(simulateScan, 1000);
                        scanIntervalGetCount = setInterval(getCount, 5000);
                    } else {
                        showError(data.msg);
                    }
                }, "json")
                    .fail(function (error) {
                        console.error('Error fetching orders:', error);
                    });
            }
        }

    }

    function stopScan() {
        readStatus = false;
        lightbulbIcon.classList.remove('on');
        lightbulbIcon.style.color = 'gray'; // 恢复默认颜色
        // if (scanning) {
        const params = {
            orderInfo: $("#recordNumber").text(),
            operation: operation
        };
        $.post('blind/stopRead', params, function (data) {
            if (data.code == "00000") {
                showSuccess("关闭成功");
                scanning = false;
                clearInterval(scanInterval);
                clearInterval(scanIntervalGetCount);
            } else {
                showError(data.msg);
            }
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });

        // }
    }

    var countInfo = 0;

    function getCount() {
        /* $.post("blind/getCount", null, function (datas) {
            if (datas.code == "00000") {
                if(datas.data!=0){
                    playVoice("扫描成功数量为" + datas.data);
                }
            }
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            }); */
    }


    function simulateScan() {
        const params = {
            orderInfo: orderId,
            operation: operation
        };
        $.post("blind/getBlindData", params, function (datas) {
            if (datas.code == "00000") {
               var rfidList = datas.data;
               var scannedItems = [];
               let orderMap = new Map();
               for(var i=0;i<orderItemListAll.length;i++){
            	   var item = orderItemListAll[i];
            	   // 提取 unReturngoodsAssets 中的 labelId 列表
                   const labelIds = item.unReturngoodsAssets.map(asset => asset.labelId);
            	   for(var j=0;j< labelIds.length;j++){
            		   if(rfidList.includes(labelIds[j])){
            			   var orderItem = orderMap.get(item.orderSn +"_"+item.skuId);
            			   if(orderItem){
            				   if(!item.labelId.includes(labelIds[j])){
            					   item.labelId.push(labelIds[j]);
                    			   item.countInfo = item.labelId.length; 
            				   }
            			   }else{
                			   item.labelId = [];
                			   item.labelId.push(labelIds[j]);
                			   item.countInfo = item.labelId.length; 
                			   scannedItems.push(item); 
                			   orderMap.set(item.orderSn +"_"+item.skuId,item);
            			   }
            		   }
            	   }
               }
               var count = 0;
                for(var k=0;k< scannedItems.length;k++){
                	count += scannedItems[k].countInfo;
                }
                $("#newCount").text(count);
                scanning = false;
                addDataToTable(scannedItems, 'scanned-data-table');
                checkSatisfaction('original-data-table'); 
            } else {
                // console.log(datas.msg
                //     alert(datas.msg)
                showError(datas.msg);
                // opt.modal.msgError(datas.msg);
            }
            // Also check the original table satisfaction
        }, "json")
            .fail(function (error) {
                console.error('Error fetching orders:', error);
            });
    }

    function checkSatisfaction(tableId) {
        const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
        for (let i = 0; i < tableBody.rows.length; i++) {
            const row = tableBody.rows[i];
            const requiredCount = parseInt(row.cells[5].textContent);
            const scannedCount = parseInt(row.cells[7]?.textContent); // Adjust based on original or scanned table
            if (requiredCount === scannedCount) {
                row.classList.add('satisfied');
            } else {
                row.classList.remove('satisfied');
            }
        }
    }

    function isEquivalent(a, b) {
        if (a.assetsRfid == b.assetsRfid) {
            return true;
        } else {
            return false;
        }
        // 自定义对象比较逻辑
    }


    function addDataToTable(orderItemList, tableId) {
        const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
        tableBody.innerHTML = ''; // 清空已有表格内容

        orderItemList.forEach(function (item, index) {
            let row = tableBody.insertRow();

            // 为每个单元格动态填充数据
            const headers = document.getElementById(tableId).getElementsByTagName('thead')[0].getElementsByTagName('th');
            // 循环表头的每一列
            for (let i = 0; i < headers.length; i++) {
                const fieldName = headers[i].getAttribute('data-field');
                let cell = row.insertCell(i);
                if (fieldName === "labelId") {
                    cell.style.display = 'none';
                }
                if (fieldName === "index") {
                    cell.textContent = index + 1; // 序号
                } else if (item.hasOwnProperty(fieldName)) {
                    cell.textContent = item[fieldName]; // 其他数据字段
                } else {
                    cell.textContent = ''; // 如果没有对应字段，清空
                }
                // 绑定点击事件（例如绑定到 "orderId" 字段）
                // 绑定点击事件（例如绑定到 "orderId" 字段）
            }
            row.style.cursor = 'pointer'; // 鼠标样式变为手型
            // cell.style.color = '#007bff'; // 添加颜色区分
            row.addEventListener('click', function () {
                // var data1 =["123","321"]
                // 直接显示弹框并传递 item 的 sentOutCodeList
                showSentOutCodePopup(item.labelId);
            });
            // 处理特定的字段逻辑，比如调整颜色
            if (item.unsentNum == 0) {
                row.style.color = '#55d948'; // 变更颜色
            }
        });
    }


    // function addDataToTable(orderItemList, tableId) {
    //     const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
    //     tableBody.innerHTML = ''; // 清空已有表格内容
    //     orderItemList.forEach(function(item) {
    //         let row = tableBody.insertRow();
    //         row.setAttribute('data-id', item.goodsId);
    //         const cell1 = row.insertCell(0);
    //         const cell2 = row.insertCell(1);
    //         const cell3 = row.insertCell(2);
    //         const cell4 = row.insertCell(3);
    //         const cell5 = row.insertCell(4);
    //         const cell6 = row.insertCell(5);
    //         const cell7 = row.insertCell(6);
    //         if(tableId=="scanned-data-table"){
    //             const cell8 = row.insertCell(7);
    //             cell8.textContent = item.countInfo;
    //             const cell9 = row.insertCell(8);
    //             cell9.hidden = true; // 添加 hidden 属性
    //             cell9.textContent = item.labelId;
    //         }
    //         cell1.textContent = item.goodsId;
    //         cell2.textContent = item.goodsName;
    //         cell3.textContent = item.skuId;
    //         cell4.textContent = item.modelCode;
    //         cell5.textContent = item.goodsNum;
    //         cell6.textContent = item.beginTime;
    //         cell7.textContent = item.endTime;
    //
    //         if (item.countInfo>item.goodsNum){
    //             row.style.color = '#ff7b7b'; // 变更颜色
    //         }
    //     });
    // }

    // function addDataToTable(list, tableId) {
    //     const tableBody = document.getElementById(tableId).getElementsByTagName('tbody')[0];
    //     tableBody.innerHTML = ''; // Clear the existing table rows
    //     list.forEach(function(tagData) {
    //         let row = tableBody.querySelector('tr[data-id="' + tagData.goodsId + '"]');
    //         if(tagData.warehouseInfo!=null && tagData.areaInfo!=null){
    //             addRow(tableBody,tableId,row,tagData);
    //
    //         }else{
    //             if (row ) {
    //                 const quantityCell = row.getElementsByTagName('td')[5];
    //                 const quantityCell2 = row.getElementsByTagName('td')[6];
    //                 quantityCell.textContent = parseInt(quantityCell.textContent) + 1;
    //                 quantityCell2.textContent = quantityCell2.textContent + "," + tagData.assetsRfid;
    //             } else {
    //                 addRow(tableBody,tableId,row,tagData);
    //             }
    //         }
    //
    //
    //
    //
    //     });
    //     checkSatisfaction(tableId); // Check satisfaction after adding data
    // }
    function addRow(tableBody, tableId, row, tagData) {
        debugger;
        console.log(tagData);
        row = tableBody.insertRow();
        row.setAttribute('data-id', tagData.goodsId);
        const cell2 = row.insertCell(0);
        const cell3 = row.insertCell(1);
        const cell4 = row.insertCell(2);
        const cell5 = row.insertCell(3);
        const cell6 = row.insertCell(4);
        const cell7 = row.insertCell(5);
        const cell8 = row.insertCell(6);
        if (tableId === "original-data-table") {
            const cell9 = row.insertCell(7);
            cell9.textContent = tagData.countScan;
        } else {
            const cell9 = row.insertCell(7);
            cell9.classList.add('display:none'); // 添加 hidden 类
            cell9.textContent = tagData.countScan;
        }
        cell2.textContent = tagData.assetsNumber;
        cell3.textContent = tagData.assetsName;
        cell4.textContent = tagData.assetsSpecifications;
        cell5.textContent = tagData.assetsTypeName;
        cell6.textContent = tagData.assetsUnitName;
        cell7.textContent = tagData.assetsCount;
        cell8.classList.add('hidden');
        cell8.textContent = tagData.assetsRfid;
        if (tagData.warehouseInfo != null) {
            const cell10 = row.insertCell(8);
            cell10.textContent = tagData.warehouseInfo;
        }
        if (tagData.areaInfo != null) {
            const cell11 = row.insertCell(9);
            cell11.textContent = tagData.areaInfo;
        }
    }

    // function loadOriginalData() {
    //     const url = baseURL + 'mainMax/getItem';
    //     const params = {
    //         orderInfo: $("#recordNumber").text(),
    //         operation: operation
    //     };
    //     $.post(url, params, function (data) {
    //         console.log(data);
    //         var count = 0;
    //         data.forEach(function (dataInfo) {
    //             count += dataInfo.assetsCount;
    //         });
    //         $("#oldCount").text(count);
    //
    //         addDataToTable(data, 'original-data-table');
    //     }, "json")
    //         .fail(function (error) {
    //             console.error('Error fetching orders:', error);
    //         });
    // }

    function addDataToScannedItems(data) {
        scannedItems.push(...data);
    }

    function clearScannedTable() {
        const tableBody = document.getElementById('scanned-data-table').getElementsByTagName('tbody')[0];
        tableBody.innerHTML = ''; // Clear the scanned table rows
        $("#newCount").text('0'); // Reset the new count
    }

    function confirmScan() {
        stopScan();
        const tableBody = document.getElementById('scanned-data-table').getElementsByTagName('tbody')[0];
        const scannedData = [];
        for (let i = 0; i < tableBody.rows.length; i++) {
            const row = tableBody.rows[i];
            console.log(row);
            const rowData = {
            	orderSn: row.cells[1].textContent,
                labelIdList: row.cells[6].textContent,
            };
            scannedData.push(rowData);
        }
        if (scannedData.length > 0) {
            $.post('blind/confirmScan',
                {
                    scannedData: JSON.stringify(scannedData),
                }, function (response) {
                    if (response.code == "00000") {
                        fetchOrderInfo(orderId);
                        showSuccess("确认成功");
                    } else {
                        showError(response.msg);
                    }
                    console.log('Confirmation response:', response);
                    scannedItems = [];
                    // loadOriginalData();
                    checkSatisfaction('original-data-table');
                    clearScannedTable();// Also check the original table satisfaction
                }, 'json')
                .fail(function (error) {
                    console.error('Error confirming scan:', error);
                    showError("确认失败");

                });
        } else {
            showError("没有数据提交失败");

        }


    }

    function showError(message) {
        const errorPopup = document.getElementById('error-popup');
        const errorMessage = document.getElementById('error-message');
        errorMessage.textContent = message;
        errorPopup.style.display = 'block';
    }

    function closeError() {
        const errorPopup = document.getElementById('error-popup');
        errorPopup.style.display = 'none';

        $.post('mainMax/errorOk',
            {}, function (response) {

            }, 'json')
            .fail(function (error) {
                console.error('Error confirming scan:', error);
                showError("确认失败");

            });

    }

    function showSuccess(message) {
        const successPopup = document.getElementById('success-popup');
        const successMessage = document.getElementById('success-message');
        successMessage.textContent = message;
        successPopup.style.display = 'block';
    }

    function closeSuccess() {
        const successPopup = document.getElementById('success-popup');
        successPopup.style.display = 'none';
    }

    // Load original data on page load
    document.addEventListener('DOMContentLoaded', function () {


        // opt.modal.error("123132")
        // layer.alert("信息提示", {icon: 6});
        // loadOriginalData();
        // scanInterval = setInterval(simulateScan, 1000);

    });
</script>
</body>
</html>
