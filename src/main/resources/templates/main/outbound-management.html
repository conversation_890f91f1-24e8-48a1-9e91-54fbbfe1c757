<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出库管理 - 出入库管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
            user-select: none;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 36px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            color: #FF9800;
        }

        .header-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .control-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 120px;
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .control-button.start {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }

        .control-button.start:hover {
            background: rgba(76, 175, 80, 0.5);
        }

        .control-button.stop {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
        }

        .control-button.stop:hover {
            background: rgba(244, 67, 54, 0.5);
        }

        .control-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 16px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #666;
        }

        .status-dot.active {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }

        .main-container {
            padding: 30px;
            height: calc(100vh - 100px);
            overflow-y: auto;
        }

        .tables-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            height: 100%;
        }

        .table-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-title {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .normal-table .table-title {
            color: #4CAF50;
        }

        .exception-table .table-title {
            color: #f44336;
        }

        .count-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            flex: 1;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 14px;
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .table-body-container {
            flex: 1;
            overflow-y: auto;
            max-height: calc(100vh - 300px);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .status-success {
            background: #4CAF50;
            color: white;
        }

        .status-error {
            background: #f44336;
            color: white;
        }

        .status-warning {
            background: #FF9800;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            opacity: 0.6;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .refresh-button {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .tables-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-arrow-up"></i> 出库管理</h1>
        <div class="header-controls">
            <div class="status-indicator">
                <div class="status-dot" id="readingStatus"></div>
                <span id="statusText">读取已停止</span>
            </div>
            <button class="control-button start" id="startReadBtn" onclick="startReading()">
                <i class="fas fa-play"></i> 开启读取
            </button>
            <button class="control-button stop" id="stopReadBtn" onclick="stopReading()" disabled>
                <i class="fas fa-stop"></i> 停止读取
            </button>
            <button class="control-button" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> 返回
            </button>
        </div>
    </div>

    <div class="main-container">
        <div class="tables-container">
            <!-- 正常出库表格 -->
            <div class="table-section normal-table">
                <div class="table-header">
                    <div class="table-title">
                        <i class="fas fa-check-circle"></i>
                        正常出库
                        <span class="count-badge" id="normalCount">0</span>
                    </div>
                    <button class="refresh-button" onclick="refreshNormalData()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="table-body-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>设备编号</th>
                                <th>设备名称</th>
                                <th>订单号</th>
                                <th>出库时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="normalTableBody">
                            <tr>
                                <td colspan="5" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <div>暂无正常出库数据</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 异常出库表格 -->
            <div class="table-section exception-table">
                <div class="table-header">
                    <div class="table-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        异常出库
                        <span class="count-badge" id="exceptionCount">0</span>
                    </div>
                    <button class="refresh-button" onclick="refreshExceptionData()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="table-body-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>设备编号</th>
                                <th>设备名称</th>
                                <th>异常类型</th>
                                <th>发现时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="exceptionTableBody">
                            <tr>
                                <td colspan="5" class="empty-state">
                                    <i class="fas fa-shield-alt"></i>
                                    <div>暂无异常出库数据</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/jquery.min.js"></script>
    <script src="/js/message-toast.js"></script>
    <script>
        let isReading = false;
        let dataRefreshInterval;

        // 开启读取
        function startReading() {
            $.post('/mainMax/startRead', function(response) {
                if (response.code === '00000') {
                    isReading = true;
                    updateReadingStatus(true);
                    startDataRefresh();
                    showMessage('读取已开启', 'success');
                } else {
                    showMessage('开启读取失败: ' + response.msg, 'error');
                }
            }).fail(function() {
                showMessage('开启读取失败: 网络错误', 'error');
            });
        }

        // 停止读取
        function stopReading() {
            $.post('/mainMax/stopRead', function(response) {
                if (response.code === '00000') {
                    isReading = false;
                    updateReadingStatus(false);
                    stopDataRefresh();
                    showMessage('读取已停止', 'success');
                } else {
                    showMessage('停止读取失败: ' + response.msg, 'error');
                }
            }).fail(function() {
                showMessage('停止读取失败: 网络错误', 'error');
            });
        }

        // 更新读取状态显示
        function updateReadingStatus(reading) {
            const statusDot = document.getElementById('readingStatus');
            const statusText = document.getElementById('statusText');
            const startBtn = document.getElementById('startReadBtn');
            const stopBtn = document.getElementById('stopReadBtn');

            if (reading) {
                statusDot.classList.add('active');
                statusText.textContent = '正在读取';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                statusDot.classList.remove('active');
                statusText.textContent = '读取已停止';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        // 开始数据刷新
        function startDataRefresh() {
            dataRefreshInterval = setInterval(() => {
                refreshNormalData();
                refreshExceptionData();
            }, 2000);
        }

        // 停止数据刷新
        function stopDataRefresh() {
            if (dataRefreshInterval) {
                clearInterval(dataRefreshInterval);
            }
        }

        // 刷新正常数据
        function refreshNormalData() {
            $.get('/getData?operation=outbound', function(response) {
                if (response.code === '00000' && response.data) {
                    updateNormalTable(response.data);
                }
            });
        }

        // 刷新异常数据
        function refreshExceptionData() {
            // 这里应该调用获取异常数据的接口
            // 暂时使用模拟数据
            const mockExceptionData = [];
            updateExceptionTable(mockExceptionData);
        }

        // 更新正常表格
        function updateNormalTable(data) {
            const tbody = document.getElementById('normalTableBody');
            const countBadge = document.getElementById('normalCount');

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <div>暂无正常出库数据</div>
                        </td>
                    </tr>
                `;
                countBadge.textContent = '0';
                return;
            }

            tbody.innerHTML = '';
            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.epcId || item.code || '--'}</td>
                    <td>${item.name || '--'}</td>
                    <td>${item.orderNo || '--'}</td>
                    <td>${new Date().toLocaleString()}</td>
                    <td><span class="status-badge status-success">正常</span></td>
                `;
                tbody.appendChild(row);
            });

            countBadge.textContent = data.length;
        }

        // 更新异常表格
        function updateExceptionTable(data) {
            const tbody = document.getElementById('exceptionTableBody');
            const countBadge = document.getElementById('exceptionCount');

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-state">
                            <i class="fas fa-shield-alt"></i>
                            <div>暂无异常出库数据</div>
                        </td>
                    </tr>
                `;
                countBadge.textContent = '0';
                return;
            }

            tbody.innerHTML = '';
            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.code}</td>
                    <td>${item.name}</td>
                    <td>${item.exceptionType}</td>
                    <td>${item.time}</td>
                    <td><span class="status-badge status-error">异常</span></td>
                `;
                tbody.appendChild(row);
            });

            countBadge.textContent = data.length;
        }

        // 显示消息
        function showMessage(message, type) {
            if (window.messageToast) {
                window.messageToast.show(message, type, 4000);
            } else {
                console.log(`${type}: ${message}`);
            }
        }

        // 返回主页
        function goBack() {
            if (isReading) {
                if (confirm('当前正在读取数据，确定要返回吗？')) {
                    stopReading();
                    setTimeout(() => {
                        window.location.href = '/index';
                    }, 500);
                }
            } else {
                window.location.href = '/index';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateReadingStatus(false);
            refreshNormalData();
            refreshExceptionData();
        });

        // 页面卸载时停止读取
        window.addEventListener('beforeunload', function() {
            if (isReading) {
                stopReading();
            }
        });
    </script>
</body>
</html>
