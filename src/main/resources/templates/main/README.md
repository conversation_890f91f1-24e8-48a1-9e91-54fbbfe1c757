# 仓储管理系统 - 大屏展示界面

## 系统概述

这是一个专为55寸大屏幕设计的仓储管理系统界面，适用于通道门展示。系统包含出库、入库、盘点三大核心模块，提供直观的数据展示和便捷的操作控制。

## 功能特性

### 🏠 主页面 (`index.html`)
- **三大模块导航**：出库管理、入库管理、库存盘点
- **实时时间显示**：页面右上角显示当前时间
- **系统状态指示**：各模块运行状态实时显示
- **响应式设计**：适配不同屏幕尺寸
- **键盘快捷键**：支持数字键1、2、3快速导航

### 📦 入库管理 (`inbound-management.html`)
- **双表格展示**：
  - 正常入库表格：显示成功入库的设备信息
  - 异常入库表格：显示入库过程中的异常情况
- **读取控制**：
  - 开启读取按钮：启动RFID设备读取
  - 停止读取按钮：停止RFID设备读取
  - 实时状态指示器
- **数据字段**：设备编号、设备名称、供应商、入库时间、状态
- **自动刷新**：读取开启时每2秒自动刷新数据

### 📤 出库管理 (`outbound-management.html`)
- **双表格展示**：
  - 正常出库表格：显示成功出库的设备信息
  - 异常出库表格：显示出库过程中的异常情况
- **读取控制**：
  - 开启读取按钮：启动RFID设备读取
  - 停止读取按钮：停止RFID设备读取
  - 实时状态指示器
- **数据字段**：设备编号、设备名称、订单号、出库时间、状态
- **自动刷新**：读取开启时每2秒自动刷新数据

### 📋 库存盘点 (`inventory-main.html`)
- **统计卡片**：
  - 总库存数量
  - 缺货商品数量
  - 超量商品数量
  - 上次盘点时间
- **库存明细表格**：商品编号、名称、分类、当前库存、标准库存、差异、状态
- **搜索功能**：支持商品名称、编号搜索
- **状态标识**：正常、缺货、超量三种状态标识

## 技术特点

### 🎨 UI设计
- **大屏优化**：专为55寸屏幕设计，按钮大小适合人工点击
- **现代化界面**：渐变背景、毛玻璃效果、动画过渡
- **高对比度**：深色背景配白色文字，适合长时间观看
- **状态指示**：LED样式的状态指示器，直观显示系统状态

### 📱 响应式设计
- **自适应布局**：支持不同屏幕尺寸
- **移动端友好**：在小屏幕设备上自动调整布局
- **触摸优化**：按钮大小适合触摸操作

### ⚡ 性能优化
- **实时数据**：通过AJAX定期刷新数据
- **状态管理**：智能的读取状态控制
- **内存管理**：及时清理定时器，避免内存泄漏

## 接口集成

### 后端接口
- `POST /mainMax/startRead` - 开启RFID读取
- `POST /mainMax/stopRead` - 停止RFID读取
- `GET /getData?operation=inbound` - 获取入库数据
- `GET /getData?operation=outbound` - 获取出库数据

### 数据格式
```javascript
// 设备数据格式
{
    epcId: "设备RFID标签ID",
    code: "设备编号", 
    name: "设备名称",
    orderNo: "订单号",
    supplier: "供应商"
}

// 响应格式
{
    code: "00000",  // 成功状态码
    msg: "操作成功",
    data: [...] // 数据数组
}
```

## 使用说明

### 🚀 启动系统
1. 访问主页：`http://your-domain/index`
2. 选择需要的功能模块
3. 在入库/出库页面点击"开启读取"开始工作

### 🔧 操作流程

#### 入库操作
1. 进入入库管理页面
2. 点击"开启读取"按钮
3. 将设备放置在RFID读取区域
4. 系统自动识别并显示在正常入库表格中
5. 异常情况会显示在异常入库表格中
6. 完成后点击"停止读取"

#### 出库操作
1. 进入出库管理页面
2. 点击"开启读取"按钮
3. 扫描需要出库的设备
4. 系统验证订单信息并显示结果
5. 完成后点击"停止读取"

#### 库存盘点
1. 进入库存盘点页面
2. 查看统计数据和库存明细
3. 使用搜索功能查找特定商品
4. 查看库存差异和状态

### ⚠️ 注意事项
- 离开页面前请先停止读取功能
- 系统会在页面卸载时自动停止读取
- 建议定期进行库存盘点以确保数据准确性
- 大屏幕建议使用全屏模式以获得最佳体验

## 文件结构

```
src/main/resources/templates/main/
├── index.html                 # 主页面
├── inbound-management.html    # 入库管理页面
├── outbound-management.html   # 出库管理页面
├── inventory-main.html        # 库存盘点页面
└── README.md                  # 说明文档

src/main/resources/static/js/
└── message-toast.js           # 消息提示组件

src/main/java/com/example/inoroutstorage/main/
└── MainController.java        # 控制器（已更新路由）
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日志

### v1.0.0 (2024-01-15)
- ✨ 新增主页面三模块导航
- ✨ 新增入库管理双表格展示
- ✨ 新增出库管理双表格展示  
- ✨ 新增库存盘点统计功能
- ✨ 新增RFID读取控制功能
- ✨ 新增消息提示组件
- ✨ 新增响应式设计支持
- ✨ 新增键盘快捷键支持
