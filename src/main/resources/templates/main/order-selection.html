<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择订单</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(to bottom, #2c3e50, #34495e);
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
        }
        .container {
            width: -webkit-fill-available;
            background-color: #1e1e1e;
            /*max-width: 800px;*/
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
        }
        tr:hover {
            background-color: #2980b9;
            cursor: pointer;
        }
        .pagination {
            text-align: center;
            display: block;
        }
        .pagination a {
            color: white;
            padding: 5px 10px;
            text-decoration: none;
            background-color: #3498db;
            margin: 0 3px;
            border-radius: 3px;
        }
        .pagination a.active {
            background-color: #2980b9;
        }
        #searchBar {
            text-align: center;
            margin-bottom: 20px;
        }
        #searchInput {
            color: black;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin-right: 10px;
            width: 60%;
            font-size: 16px;
        }
        #searchInput:focus {
            outline: none;
            box-shadow: 0 0 5px #3498db;
        }
        #searchButton {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background-color: #3498db;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        #searchButton:hover {
            background-color: #2980b9;
        }
        button.return-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s;
        }

        button.return-button:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
<script src="/js/jquery.min.js"></script>
<div class="container">
    <button type="button" class="return-button" onclick="goBack()">返回</button>
    <button type="button" class="return-button" id="blind" onclick="blindReceipt()">盲收</button>
    <h1>选择订单</h1>
    <div id="searchBar">
        <input type="text" id="searchInput" placeholder="搜索订单...">
        <button id="searchButton" onclick="searchOrders()">搜索</button>
    </div>
    <table id="orderTable">
        <thead>
        <tr>
            <th>订单号</th>
            <th>节目名称</th>
            <th>节目部门</th>
            <th>下单人</th>
            <th>租借开始时间</th>
            <th>租借结束时间</th>
            <th>设备数量</th>
            <th>创建时间</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody id="orderList">
        </tbody>
    </table>
    <div class="pagination" id="pagination">
    </div>
</div>
<script>
    const urlParams = new URLSearchParams(window.location.search);
    const operation = urlParams.get('operation'); // 替换 'paramName' 为实际的参数名
    
    if(operation === "outbound"){
    	$('#blind').hide();
    }

    const itemsPerPage = 5; // 每页显示的订单数
    let currentPage = 1; // 当前页码
    let totalItems = 0; // 订单总数
    let search = ""; // 订单总数
    function goBack() {
        window.location.href = "/index";
    }
    function searchOrders() {
         search = document.getElementById('searchInput').value;
        fetchOrders(1, search);
    }

    function fetchOrders(page) {
        currentPage = page;
        const url = 'mainMax/getOrder';
        const params = {
            operation: operation,
            pageSize: itemsPerPage,
            pageNumber: currentPage,
            searchKey: search
        };

        $.post(url, params, function(data) {
            if(data.code == '403'){
                window.location.href = "login";
            }else{
                const { records, total } = data.data;
                totalItems = total;
                displayOrders(records);
                displayPagination();
            }

        }, "json")
            .fail(function(error) {
                console.error('Error fetching orders:', error);
            });
    }

    function blindReceipt(orderId) {
        window.location.href = "blindReceipt?orderId="+orderId+"&operation="+operation;
    }
    function selectOrder(orderId) {
        window.location.href = "storage-entry?orderId="+orderId+"&operation="+operation;
    }

    function displayOrders(orders) {
        const orderList = document.getElementById('orderList');
        orderList.innerHTML = ''; // 清空之前的内容
        for (const order of orders) {
            const tr = document.createElement('tr');
            tr.onclick = function() {
                selectOrder(order.orderSn);
            };
            const tdorderSn = document.createElement('td');
            tdorderSn.textContent = order.orderSn;
            const tdprogramName = document.createElement('td');
            tdprogramName.textContent = order.programName;
            const tdprogramDept = document.createElement('td');
            tdprogramDept.textContent = order.programDept;
            const tdmemberName = document.createElement('td');
            tdmemberName.textContent = order.memberName;
            const tdbeginTime = document.createElement('td');
            tdbeginTime.textContent = order.beginTime;
            const tdendTime = document.createElement('td');
            tdendTime.textContent = order.endTime;
            const tdgoodsNum = document.createElement('td');
            tdgoodsNum.textContent = order.goodsNum;
            const tdcreateTime = document.createElement('td');
            tdcreateTime.textContent = order.createTime;
            const tdremark = document.createElement('td');
            tdremark.remark = order.remark;
            tr.appendChild(tdorderSn);
            tr.appendChild(tdprogramName);
            tr.appendChild(tdprogramName);
            tr.appendChild(tdprogramDept);
            tr.appendChild(tdmemberName);
            tr.appendChild(tdbeginTime);
            tr.appendChild(tdendTime);
            tr.appendChild(tdgoodsNum);
            tr.appendChild(tdcreateTime);
            tr.appendChild(tdremark);
            orderList.appendChild(tr);
        }
    }

    function displayPagination() {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = ''; // 清空之前的内容

        const prevButton = document.createElement('a');
        prevButton.href = '#';
        prevButton.textContent = '上一页';
        prevButton.onclick = function() {
            if (currentPage > 1) {
                fetchOrders(currentPage - 1);
            }
        };
        pagination.appendChild(prevButton);

        const maxVisiblePages = 5; // 最多显示的页码数量
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const link = document.createElement('a');
            link.href = '#';
            link.textContent = i;
            if (i === currentPage) {
                link.className = 'active';
            }
            link.onclick = function() {
                fetchOrders(i);
            };
            pagination.appendChild(link);
        }

        const nextButton = document.createElement('a');
        nextButton.href = '#';
        nextButton.textContent = '下一页';
        nextButton.onclick = function() {
            if (currentPage < totalPages) {
                fetchOrders(currentPage + 1);
            }
        };
        pagination.appendChild(nextButton);
    }

    fetchOrders(currentPage);
</script>
</body>
</html>
