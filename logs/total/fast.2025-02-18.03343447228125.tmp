2025-02-18 09:24:42.795  INFO 18992 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:24:44.580  INFO 33297 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33297 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:24:44.585  INFO 33297 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:24:45.424  INFO 33297 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-02-18 09:24:45.436  INFO 33297 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:24:45.436  INFO 33297 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:24:45.540  INFO 33297 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:24:45.540  INFO 33297 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 906 ms
2025-02-18 09:24:45.721  INFO 33297 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:24:45.865  INFO 33297 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:24:45.873  INFO 33297 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.667 seconds (JVM running for 2.18)
2025-02-18 09:24:45.948  INFO 33297 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xd32ab83b] REGISTERED
2025-02-18 09:24:45.949  INFO 33297 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xd32ab83b] CONNECT: /**********:23
2025-02-18 09:25:10.769  INFO 33297 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:25:10.773  INFO 33297 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:25:10.773  INFO 33297 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:25:10.774  INFO 33297 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:25:16.016  INFO 33297 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xd32ab83b] CLOSE
2025-02-18 09:25:16.019  INFO 33297 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xd32ab83b] UNREGISTERED
2025-02-18 09:25:37.990  INFO 33297 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:25:39.096  INFO 33392 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33392 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:25:39.098  INFO 33392 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:25:39.522  INFO 33392 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:25:39.526  INFO 33392 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:25:39.527  INFO 33392 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:25:39.557  INFO 33392 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:25:39.557  INFO 33392 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 431 ms
2025-02-18 09:25:39.676  INFO 33392 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:25:39.802  INFO 33392 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:25:39.808  INFO 33392 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.022 seconds (JVM running for 1.39)
2025-02-18 09:25:39.857  INFO 33392 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x6190f41e] REGISTERED
2025-02-18 09:25:39.857  INFO 33392 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x6190f41e] CONNECT: /**********:23
2025-02-18 09:25:43.677  INFO 33392 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:25:43.682  INFO 33392 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:25:43.683  INFO 33392 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:25:43.684  INFO 33392 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:25:56.031  INFO 33392 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x6190f41e] CLOSE
2025-02-18 09:25:56.032  INFO 33392 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x6190f41e] UNREGISTERED
2025-02-18 09:25:56.045  INFO 33392 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:25:57.761  INFO 33410 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33410 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:25:57.764  INFO 33410 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:25:58.177  INFO 33410 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:25:58.182  INFO 33410 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:25:58.182  INFO 33410 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:25:58.215  INFO 33410 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:25:58.215  INFO 33410 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 424 ms
2025-02-18 09:25:58.313  INFO 33410 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:25:58.407  INFO 33410 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:25:58.413  INFO 33410 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.06 seconds (JVM running for 1.531)
2025-02-18 09:25:58.459  INFO 33410 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb597ddaa] REGISTERED
2025-02-18 09:25:58.459  INFO 33410 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb597ddaa] CONNECT: /**********:23
2025-02-18 09:26:00.378  INFO 33410 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:26:00.384  INFO 33410 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:26:00.384  INFO 33410 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:26:00.385  INFO 33410 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:26:15.110  INFO 33410 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb597ddaa] CLOSE
2025-02-18 09:26:15.112  INFO 33410 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb597ddaa] UNREGISTERED
2025-02-18 09:26:15.124  INFO 33410 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:26:16.731  INFO 33431 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33431 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:26:16.733  INFO 33431 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:26:17.125  INFO 33431 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:26:17.129  INFO 33431 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:26:17.129  INFO 33431 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:26:17.156  INFO 33431 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:26:17.156  INFO 33431 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 400 ms
2025-02-18 09:26:17.259  INFO 33431 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:26:17.354  INFO 33431 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:26:17.360  INFO 33431 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.886 seconds (JVM running for 1.354)
2025-02-18 09:26:17.408  INFO 33431 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0dfde084] REGISTERED
2025-02-18 09:26:17.409  INFO 33431 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0dfde084] CONNECT: /**********:23
2025-02-18 09:26:47.414  INFO 33431 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0dfde084] CLOSE
2025-02-18 09:26:47.421  INFO 33431 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0dfde084] UNREGISTERED
2025-02-18 09:27:04.407  INFO 33431 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:27:04.412  INFO 33431 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:27:04.412  INFO 33431 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:27:04.413  INFO 33431 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:28:03.039  INFO 33431 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:28:05.760  INFO 33547 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33547 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:28:05.761  INFO 33547 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:28:06.165  INFO 33547 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:28:06.169  INFO 33547 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:28:06.169  INFO 33547 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:28:06.194  INFO 33547 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:28:06.194  INFO 33547 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 412 ms
2025-02-18 09:28:06.301  INFO 33547 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:28:06.397  INFO 33547 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:28:06.402  INFO 33547 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.948 seconds (JVM running for 1.295)
2025-02-18 09:28:06.454  INFO 33547 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x5a362178] REGISTERED
2025-02-18 09:28:06.455  INFO 33547 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x5a362178] CONNECT: /**********:23
2025-02-18 09:28:15.279  INFO 33547 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:28:15.284  INFO 33547 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:28:15.284  INFO 33547 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:28:15.285  INFO 33547 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:28:36.458  INFO 33547 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x5a362178] CLOSE
2025-02-18 09:28:36.460  INFO 33547 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x5a362178] UNREGISTERED
2025-02-18 09:29:23.675  INFO 33547 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:29:26.246  INFO 33630 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33630 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:29:26.248  INFO 33630 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:29:26.647  INFO 33630 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:29:26.651  INFO 33630 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:29:26.651  INFO 33630 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:29:26.674  INFO 33630 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:29:26.675  INFO 33630 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 407 ms
2025-02-18 09:29:26.781  INFO 33630 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:29:26.874  INFO 33630 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:29:26.880  INFO 33630 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.877 seconds (JVM running for 1.25)
2025-02-18 09:29:26.929  INFO 33630 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xffa0d9e4] REGISTERED
2025-02-18 09:29:26.929  INFO 33630 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xffa0d9e4] CONNECT: /**********:23
2025-02-18 09:29:36.243  INFO 33630 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:29:36.248  INFO 33630 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:29:36.248  INFO 33630 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:29:36.249  INFO 33630 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:29:51.408 ERROR 33630 --- [http-nio-8080-exec-5] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.alibaba.fastjson.JSONException: syntax error, expect {, actual [, pos 82, fieldName result, fastjson-version 1.2.73] with root cause

com.alibaba.fastjson.JSONException: syntax error, expect {, actual [, pos 82, fieldName result, fastjson-version 1.2.73
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:515)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1613)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_5_OrderResult.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:296)
	at com.alibaba.fastjson.parser.deserializer.DefaultFieldDeserializer.parseField(DefaultFieldDeserializer.java:86)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseField(JavaBeanDeserializer.java:1277)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:892)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1613)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_4_OrderResponse.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:296)
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseObject(DefaultJSONParser.java:694)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:395)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:299)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:572)
	at com.example.inoroutstorage.main.MainMaxController.getOrderInfoBlindReceipt(MainMaxController.java:323)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:29:56.935  INFO 33630 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xffa0d9e4] CLOSE
2025-02-18 09:29:56.937  INFO 33630 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xffa0d9e4] UNREGISTERED
2025-02-18 09:34:04.549  WARN 33630 --- [RMI TCP Accept-0] sun.rmi.transport.tcp                    : RMI TCP Accept-0: accept loop for ServerSocket[addr=0.0.0.0/0.0.0.0,localport=59795] throws

java.io.IOException: The server sockets created using the LocalRMIServerSocketFactory only accept connections from clients running on the host where the RMI remote objects have been exported.
	at sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:114)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:405)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:34:04.552  WARN 33630 --- [RMI TCP Accept-0] sun.rmi.transport.tcp                    : RMI TCP Accept-0: accept loop for ServerSocket[addr=0.0.0.0/0.0.0.0,localport=59795] throws

java.io.IOException: The server sockets created using the LocalRMIServerSocketFactory only accept connections from clients running on the host where the RMI remote objects have been exported.
	at sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:114)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:405)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:34:04.550 ERROR 33630 --- [http-nio-8080-exec-9] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.alibaba.fastjson.JSONException: syntax error, expect {, actual [, pos 82, fieldName result, fastjson-version 1.2.73] with root cause

com.alibaba.fastjson.JSONException: syntax error, expect {, actual [, pos 82, fieldName result, fastjson-version 1.2.73
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:515)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1613)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_5_OrderResult.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:296)
	at com.alibaba.fastjson.parser.deserializer.DefaultFieldDeserializer.parseField(DefaultFieldDeserializer.java:86)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseField(JavaBeanDeserializer.java:1277)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:892)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1613)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_4_OrderResponse.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:296)
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseObject(DefaultJSONParser.java:694)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:395)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:299)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:572)
	at com.example.inoroutstorage.main.MainMaxController.getOrderInfoBlindReceipt(MainMaxController.java:323)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:34:04.553  WARN 33630 --- [RMI TCP Accept-0] sun.rmi.transport.tcp                    : RMI TCP Accept-0: accept loop for ServerSocket[addr=0.0.0.0/0.0.0.0,localport=59795] throws

java.io.IOException: The server sockets created using the LocalRMIServerSocketFactory only accept connections from clients running on the host where the RMI remote objects have been exported.
	at sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:114)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:405)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:34:04.634  INFO 33630 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:34:20.947  INFO 33919 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 33919 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:34:20.949  INFO 33919 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:34:21.421  INFO 33919 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:34:21.426  INFO 33919 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:34:21.426  INFO 33919 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:34:21.473  INFO 33919 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:34:21.473  INFO 33919 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 499 ms
2025-02-18 09:34:21.605  INFO 33919 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:34:21.722  INFO 33919 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:34:21.728  INFO 33919 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.037 seconds (JVM running for 1.326)
2025-02-18 09:34:21.784  INFO 33919 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0db117ae] REGISTERED
2025-02-18 09:34:21.784  INFO 33919 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0db117ae] CONNECT: /**********:23
2025-02-18 09:34:51.792  INFO 33919 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0db117ae] CLOSE
2025-02-18 09:34:51.803  INFO 33919 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x0db117ae] UNREGISTERED
2025-02-18 09:36:26.746  INFO 33919 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:36:26.751  INFO 33919 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:36:26.751  INFO 33919 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:36:26.752  INFO 33919 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:36:33.476 ERROR 33919 --- [http-nio-8080-exec-5] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.alibaba.fastjson.JSONException: illegal identifier : \pos 1, line 1, column 2{\\"success\\":true,\\"message\\":\\"success\\",\\"code\\":200,\\"timestamp\\":1739842593492,\\"result\\":[{\\"order\\":{\\"orderSn\\":\\"O202502171891393369448505345\\",\\"programName\\":\\"盲收测试4\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"2\\",\\"createTime\\":\\"2025-02-17 15:44:54.352000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":2},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891393369511419904\\",\\"goodsId\\":\\"1891384951174184962\\",\\"goodsName\\":\\"吊装测试 吊装测试\\",\\"skuId\\":\\"1891384951685890049\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/05a9eeef3c8b4a42bd4ff7819ce868ab.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"outCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"smgCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"吊装测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891389511536062466\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:29:35\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7D\\",\\"outCode\\":\\"4DCD000101A0000000219B7D\\",\\"smgCode\\":\\"4DCD000101A0000000219B7D\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392267919409153\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:32\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD43C120\\",\\"outCode\\":\\"E28011C1A500006EAD43C120\\",\\"smgCode\\":\\"E28011C1A500006EAD43C120\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891389511536062466\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:29:35\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7D\\",\\"outCode\\":\\"4DCD000101A0000000219B7D\\",\\"smgCode\\":\\"4DCD000101A0000000219B7D\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392267919409153\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:32\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD43C120\\",\\"outCode\\":\\"E28011C1A500006EAD43C120\\",\\"smgCode\\":\\"E28011C1A500006EAD43C120\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"4DCD000101A0000000219B7D\\",\\"E28011C1A500006EAD43C120\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]},{\\"order\\":{\\"orderSn\\":\\"O202502171891393067831922689\\",\\"programName\\":\\"盲收测试3\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"4\\",\\"createTime\\":\\"2025-02-17 15:43:42.574000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":4},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891393068431708160\\",\\"goodsId\\":\\"1891385097463115777\\",\\"goodsName\\":\\"测试内通 内通测试\\",\\"skuId\\":\\"1891385098012569601\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/9efb1d65245242fbab48ea946d88a24f.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"outCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"smgCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"内通测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392434127089666\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:11\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD40CD60\\",\\"outCode\\":\\"E28011C1A500006EAD40CD60\\",\\"smgCode\\":\\"E28011C1A500006EAD40CD60\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392484383240193\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:23\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B77\\",\\"outCode\\":\\"4DCD000101A0000000219B77\\",\\"smgCode\\":\\"4DCD000101A0000000219B77\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392434127089666\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:11\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD40CD60\\",\\"outCode\\":\\"E28011C1A500006EAD40CD60\\",\\"smgCode\\":\\"E28011C1A500006EAD40CD60\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392484383240193\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:23\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B77\\",\\"outCode\\":\\"4DCD000101A0000000219B77\\",\\"smgCode\\":\\"4DCD000101A0000000219B77\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD40CD60\\",\\"4DCD000101A0000000219B77\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891393068469456897\\",\\"goodsId\\":\\"1891385215335645186\\",\\"goodsName\\":\\"测试特效 测试特效\\",\\"skuId\\":\\"1891385215356616706\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/d33fca2c117945acb187980b4caaf1b8.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"outCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"smgCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"测试特效\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392137342337026\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:01\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7C\\",\\"outCode\\":\\"4DCD000101A0000000219B7C\\",\\"smgCode\\":\\"4DCD000101A0000000219B7C\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392033571057665\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:39:36\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD444570\\",\\"outCode\\":\\"E28011C1A500006EAD444570\\",\\"smgCode\\":\\"E28011C1A500006EAD444570\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392137342337026\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:01\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7C\\",\\"outCode\\":\\"4DCD000101A0000000219B7C\\",\\"smgCode\\":\\"4DCD000101A0000000219B7C\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392033571057665\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:39:36\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD444570\\",\\"outCode\\":\\"E28011C1A500006EAD444570\\",\\"smgCode\\":\\"E28011C1A500006EAD444570\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"4DCD000101A0000000219B7C\\",\\"E28011C1A500006EAD444570\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]},{\\"order\\":{\\"orderSn\\":\\"O202502171891392876244492289\\",\\"programName\\":\\"盲收测试2\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"4\\",\\"createTime\\":\\"2025-02-17 15:42:56.771000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":4},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891392876374515713\\",\\"goodsId\\":\\"1891384951174184962\\",\\"goodsName\\":\\"吊装测试 吊装测试\\",\\"skuId\\":\\"1891384951685890049\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":1,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":1,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":1,\\"optDeliverNum\\":0,\\"unReturnNum\\":1,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/05a9eeef3c8b4a42bd4ff7819ce868ab.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"outCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"吊装测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392331588943873\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:47\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD4185F3\\",\\"outCode\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCode\\":\\"E28011C1A500006EAD4185F3\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392331588943873\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:47\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD4185F3\\",\\"outCode\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCode\\":\\"E28011C1A500006EAD4185F3\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD4185F3\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891392876303212545\\",\\"goodsId\\":\\"1891385097463115777\\",\\"goodsName\\":\\"测试内通 内通测试\\",\\"skuId\\":\\"1891385098012569601\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/9efb1d65245242fbab48ea946d88a24f.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"outCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"smgCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"内通测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392544391147521\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:38\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD44ED23\\",\\"outCode\\":\\"E28011C1A500006EAD44ED23\\",\\"smgCode\\":\\"E28011C1A500006EAD44ED23\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392604478746626\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:52\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD410110\\",\\"outCode\\":\\"E28011C1A500006EAD410110\\",\\"smgCode\\":\\"E28011C1A500006EAD410110\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392544391147521\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:38\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD44ED23\\",\\"outCode\\":\\"E28011C1A500006EAD44ED23\\",\\"smgCode\\":\\"E28011C1A500006EAD44ED23\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392604478746626\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:52\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD410110\\",\\"outCode\\":\\"E28011C1A500006EAD410110\\",\\"smgCode\\":\\"E28011C1A500006EAD410110\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD44ED23\\",\\"E28011C1A500006EAD410110\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891392876345155584\\",\\"goodsId\\":\\"1891385215335645186\\",\\"goodsName\\":\\"测试特效 测试特效\\",\\"skuId\\":\\"1891385215356616706\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":1,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":1,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":1,\\"optDeliverNum\\":0,\\"unReturnNum\\":1,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/d33fca2c117945acb187980b4caaf1b8.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD47C970\\",\\"outCodes\\":\\"E28011C1A500006EAD47C970\\",\\"smgCodes\\":\\"E28011C1A500006EAD47C970\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"测试特效\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392209048158209\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:18\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD47C970\\",\\"outCode\\":\\"E28011C1A500006EAD47C970\\",\\"smgCode\\":\\"E28011C1A500006EAD47C970\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392209048158209\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:18\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD47C970\\",\\"outCode\\":\\"E28011C1A500006EAD47C970\\",\\"smgCode\\":\\"E28011C1A500006EAD47C970\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD47C970\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]}]}] with root cause

com.alibaba.fastjson.JSONException: illegal identifier : \pos 1, line 1, column 2{\\"success\\":true,\\"message\\":\\"success\\",\\"code\\":200,\\"timestamp\\":1739842593492,\\"result\\":[{\\"order\\":{\\"orderSn\\":\\"O202502171891393369448505345\\",\\"programName\\":\\"盲收测试4\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"2\\",\\"createTime\\":\\"2025-02-17 15:44:54.352000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":2},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891393369511419904\\",\\"goodsId\\":\\"1891384951174184962\\",\\"goodsName\\":\\"吊装测试 吊装测试\\",\\"skuId\\":\\"1891384951685890049\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/05a9eeef3c8b4a42bd4ff7819ce868ab.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"outCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"smgCodes\\":\\"4DCD000101A0000000219B7D,E28011C1A500006EAD43C120\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"吊装测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891389511536062466\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:29:35\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7D\\",\\"outCode\\":\\"4DCD000101A0000000219B7D\\",\\"smgCode\\":\\"4DCD000101A0000000219B7D\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392267919409153\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:32\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD43C120\\",\\"outCode\\":\\"E28011C1A500006EAD43C120\\",\\"smgCode\\":\\"E28011C1A500006EAD43C120\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891389511536062466\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:29:35\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7D\\",\\"outCode\\":\\"4DCD000101A0000000219B7D\\",\\"smgCode\\":\\"4DCD000101A0000000219B7D\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392267919409153\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:32\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:20\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD43C120\\",\\"outCode\\":\\"E28011C1A500006EAD43C120\\",\\"smgCode\\":\\"E28011C1A500006EAD43C120\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"4DCD000101A0000000219B7D\\",\\"E28011C1A500006EAD43C120\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]},{\\"order\\":{\\"orderSn\\":\\"O202502171891393067831922689\\",\\"programName\\":\\"盲收测试3\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"4\\",\\"createTime\\":\\"2025-02-17 15:43:42.574000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":4},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891393068431708160\\",\\"goodsId\\":\\"1891385097463115777\\",\\"goodsName\\":\\"测试内通 内通测试\\",\\"skuId\\":\\"1891385098012569601\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/9efb1d65245242fbab48ea946d88a24f.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"outCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"smgCodes\\":\\"E28011C1A500006EAD40CD60,4DCD000101A0000000219B77\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"内通测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392434127089666\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:11\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD40CD60\\",\\"outCode\\":\\"E28011C1A500006EAD40CD60\\",\\"smgCode\\":\\"E28011C1A500006EAD40CD60\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392484383240193\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:23\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B77\\",\\"outCode\\":\\"4DCD000101A0000000219B77\\",\\"smgCode\\":\\"4DCD000101A0000000219B77\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392434127089666\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:11\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD40CD60\\",\\"outCode\\":\\"E28011C1A500006EAD40CD60\\",\\"smgCode\\":\\"E28011C1A500006EAD40CD60\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392484383240193\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:23\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B77\\",\\"outCode\\":\\"4DCD000101A0000000219B77\\",\\"smgCode\\":\\"4DCD000101A0000000219B77\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD40CD60\\",\\"4DCD000101A0000000219B77\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891393068469456897\\",\\"goodsId\\":\\"1891385215335645186\\",\\"goodsName\\":\\"测试特效 测试特效\\",\\"skuId\\":\\"1891385215356616706\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/d33fca2c117945acb187980b4caaf1b8.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"outCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"smgCodes\\":\\"4DCD000101A0000000219B7C,E28011C1A500006EAD444570\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"测试特效\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392137342337026\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:01\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7C\\",\\"outCode\\":\\"4DCD000101A0000000219B7C\\",\\"smgCode\\":\\"4DCD000101A0000000219B7C\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392033571057665\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:39:36\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD444570\\",\\"outCode\\":\\"E28011C1A500006EAD444570\\",\\"smgCode\\":\\"E28011C1A500006EAD444570\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392137342337026\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:01\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"4DCD000101A0000000219B7C\\",\\"outCode\\":\\"4DCD000101A0000000219B7C\\",\\"smgCode\\":\\"4DCD000101A0000000219B7C\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392033571057665\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:39:36\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:10\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD444570\\",\\"outCode\\":\\"E28011C1A500006EAD444570\\",\\"smgCode\\":\\"E28011C1A500006EAD444570\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"4DCD000101A0000000219B7C\\",\\"E28011C1A500006EAD444570\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]},{\\"order\\":{\\"orderSn\\":\\"O202502171891392876244492289\\",\\"programName\\":\\"盲收测试2\\",\\"programDept\\":\\"\\",\\"memberName\\":\\"李宁\\",\\"beginTime\\":\\"2025-02-18 00:00:00\\",\\"endTime\\":\\"2025-02-19 00:00:00\\",\\"goodsNum\\":\\"4\\",\\"createTime\\":\\"2025-02-17 15:42:56.771000\\",\\"remark\\":\\"\\",\\"curReturnNum\\":4},\\"orderItemList\\":[{\\"sn\\":\\"OI202502171891392876374515713\\",\\"goodsId\\":\\"1891384951174184962\\",\\"goodsName\\":\\"吊装测试 吊装测试\\",\\"skuId\\":\\"1891384951685890049\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":1,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":1,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":1,\\"optDeliverNum\\":0,\\"unReturnNum\\":1,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/05a9eeef3c8b4a42bd4ff7819ce868ab.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"outCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCodes\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"吊装测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392331588943873\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:47\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD4185F3\\",\\"outCode\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCode\\":\\"E28011C1A500006EAD4185F3\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392331588943873\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:47\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"吊装测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD4185F3\\",\\"outCode\\":\\"E28011C1A500006EAD4185F3\\",\\"smgCode\\":\\"E28011C1A500006EAD4185F3\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD4185F3\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891392876303212545\\",\\"goodsId\\":\\"1891385097463115777\\",\\"goodsName\\":\\"测试内通 内通测试\\",\\"skuId\\":\\"1891385098012569601\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":2,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":2,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":2,\\"optDeliverNum\\":0,\\"unReturnNum\\":2,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/9efb1d65245242fbab48ea946d88a24f.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"outCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"smgCodes\\":\\"E28011C1A500006EAD44ED23,E28011C1A500006EAD410110\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"内通测试\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392544391147521\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:38\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD44ED23\\",\\"outCode\\":\\"E28011C1A500006EAD44ED23\\",\\"smgCode\\":\\"E28011C1A500006EAD44ED23\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392604478746626\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:52\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD410110\\",\\"outCode\\":\\"E28011C1A500006EAD410110\\",\\"smgCode\\":\\"E28011C1A500006EAD410110\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392544391147521\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:38\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD44ED23\\",\\"outCode\\":\\"E28011C1A500006EAD44ED23\\",\\"smgCode\\":\\"E28011C1A500006EAD44ED23\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null},{\\"id\\":\\"1891392604478746626\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:41:52\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"内通测试\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD410110\\",\\"outCode\\":\\"E28011C1A500006EAD410110\\",\\"smgCode\\":\\"E28011C1A500006EAD410110\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD44ED23\\",\\"E28011C1A500006EAD410110\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"},{\\"sn\\":\\"OI202502171891392876345155584\\",\\"goodsId\\":\\"1891385215335645186\\",\\"goodsName\\":\\"测试特效 测试特效\\",\\"skuId\\":\\"1891385215356616706\\",\\"num\\":null,\\"transferGoodsNum\\":null,\\"goodsNum\\":1,\\"unsentNum\\":0,\\"returnNum\\":0,\\"returnFinishedNum\\":0,\\"repairNum\\":0,\\"curReturnNum\\":1,\\"transferNum\\":0,\\"deferNum\\":0,\\"optReturnNum\\":1,\\"optDeliverNum\\":0,\\"unReturnNum\\":1,\\"image\\":\\"https://common-api.frtp-zhzc.cn/common/image/d33fca2c117945acb187980b4caaf1b8.png?x-oss-process=style/400X400\\",\\"name\\":null,\\"goodsPrice\\":99.0,\\"beginTime\\":\\"2025-02-18 00:00\\",\\"endTime\\":\\"2025-02-19 00:00\\",\\"inCodes\\":\\"E28011C1A500006EAD47C970\\",\\"outCodes\\":\\"E28011C1A500006EAD47C970\\",\\"smgCodes\\":\\"E28011C1A500006EAD47C970\\",\\"smgCodeList\\":null,\\"modelCode\\":\\"测试特效\\",\\"attribute\\":\\"1\\",\\"checked\\":false,\\"remark\\":\\"\\",\\"receivedUser\\":null,\\"goodsAssets\\":[{\\"id\\":\\"1891392209048158209\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:18\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD47C970\\",\\"outCode\\":\\"E28011C1A500006EAD47C970\\",\\"smgCode\\":\\"E28011C1A500006EAD47C970\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"unReturngoodsAssets\\":[{\\"id\\":\\"1891392209048158209\\",\\"createBy\\":\\"admin\\",\\"createTime\\":\\"2025-02-17 15:40:18\\",\\"updateBy\\":\\"admin\\",\\"updateTime\\":\\"2025-02-17 15:45:34\\",\\"deleteFlag\\":false,\\"modelCode\\":\\"测试特效\\",\\"type\\":\\"1\\",\\"attribute\\":\\"1\\",\\"inCode\\":\\"E28011C1A500006EAD47C970\\",\\"outCode\\":\\"E28011C1A500006EAD47C970\\",\\"smgCode\\":\\"E28011C1A500006EAD47C970\\",\\"status\\":\\"1\\",\\"expirationDate\\":null,\\"fapiaoImg\\":null,\\"receivingNoteImg\\":null,\\"distributionCenterCode\\":null,\\"location\\":null,\\"remark\\":null,\\"isZj\\":1,\\"useStatus\\":\\"OUT\\",\\"lastOrderItemSn\\":null,\\"ascription\\":null,\\"price\\":null,\\"buyDate\\":null,\\"remoteId\\":null}],\\"transferAssets\\":[],\\"deliveredAssets\\":null,\\"sentOutCodeList\\":[\\"E28011C1A500006EAD47C970\\"],\\"selectAssetIds\\":[],\\"afterSaleStatus\\":\\"NEW\\",\\"complainStatus\\":\\"NEW\\",\\"commentStatus\\":\\"UNFINISHED\\"}]}]}
	at com.alibaba.fastjson.parser.JSONLexerBase.scanSymbolUnQuoted(JSONLexerBase.java:832)
	at com.alibaba.fastjson.parser.JSONLexerBase.scanSymbol(JSONLexerBase.java:633)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:718)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.parseRest(JavaBeanDeserializer.java:1613)
	at com.alibaba.fastjson.parser.deserializer.FastjsonASMDeserializer_4_OrderResponse.deserialze(Unknown Source)
	at com.alibaba.fastjson.parser.deserializer.JavaBeanDeserializer.deserialze(JavaBeanDeserializer.java:296)
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseObject(DefaultJSONParser.java:694)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:395)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:299)
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:572)
	at com.example.inoroutstorage.main.MainMaxController.getOrderInfoBlindReceipt(MainMaxController.java:323)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-02-18 09:37:34.277  INFO 33919 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 09:37:37.521  INFO 34188 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 34188 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:37:37.522  INFO 34188 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:37:37.962  INFO 34188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:37:37.966  INFO 34188 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:37:37.966  INFO 34188 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:37:37.995  INFO 34188 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:37:37.995  INFO 34188 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 451 ms
2025-02-18 09:37:38.112  INFO 34188 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:37:38.212  INFO 34188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:37:38.218  INFO 34188 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.95 seconds (JVM running for 1.373)
2025-02-18 09:37:38.267  INFO 34188 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x82bab57b] REGISTERED
2025-02-18 09:37:38.267  INFO 34188 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x82bab57b] CONNECT: /**********:23
2025-02-18 09:38:08.271  INFO 34188 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x82bab57b] CLOSE
2025-02-18 09:38:08.273  INFO 34188 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x82bab57b] UNREGISTERED
2025-02-18 09:39:42.937  INFO 34307 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 34307 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 09:39:42.938  INFO 34307 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 09:39:43.345  INFO 34307 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 09:39:43.349  INFO 34307 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 09:39:43.350  INFO 34307 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 09:39:43.382  INFO 34307 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 09:39:43.382  INFO 34307 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 425 ms
2025-02-18 09:39:43.508  INFO 34307 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 09:39:43.615  INFO 34307 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 09:39:43.621  INFO 34307 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.907 seconds (JVM running for 1.266)
2025-02-18 09:39:43.670  INFO 34307 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x7506cc67] REGISTERED
2025-02-18 09:39:43.670  INFO 34307 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x7506cc67] CONNECT: /**********:23
2025-02-18 09:40:04.735  INFO 34307 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1736086390,1736329813,1736754798,1737000296; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 09:40:04.740  INFO 34307 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 09:40:04.740  INFO 34307 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 09:40:04.741  INFO 34307 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 09:40:13.675  INFO 34307 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x7506cc67] CLOSE
2025-02-18 09:40:13.682  INFO 34307 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x7506cc67] UNREGISTERED
2025-02-18 10:50:12.583  INFO 39898 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 39898 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 10:50:12.585  INFO 39898 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 10:50:13.023  INFO 39898 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 10:50:13.028  INFO 39898 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 10:50:13.028  INFO 39898 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 10:50:13.057  INFO 39898 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 10:50:13.057  INFO 39898 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 444 ms
2025-02-18 10:50:13.175  INFO 39898 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 10:50:13.276  WARN 39898 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-02-18 10:50:13.277  INFO 39898 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 10:50:13.278  INFO 39898 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-02-18 10:50:13.285  INFO 39898 --- [main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-02-18 10:50:13.294 ERROR 39898 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-02-18 10:51:16.742  INFO 39962 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 39962 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 10:51:16.744  INFO 39962 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 10:51:17.162  INFO 39962 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 10:51:17.166  INFO 39962 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 10:51:17.166  INFO 39962 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 10:51:17.195  INFO 39962 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 10:51:17.195  INFO 39962 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 427 ms
2025-02-18 10:51:17.322  INFO 39962 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 10:51:17.426  WARN 39962 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-02-18 10:51:17.426  INFO 39962 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-18 10:51:17.428  INFO 39962 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-02-18 10:51:17.434  INFO 39962 --- [main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-02-18 10:51:17.443 ERROR 39962 --- [main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-02-18 10:51:24.956  INFO 39978 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 39978 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 10:51:24.957  INFO 39978 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 10:51:25.346  INFO 39978 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 10:51:25.350  INFO 39978 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 10:51:25.350  INFO 39978 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 10:51:25.405  INFO 39978 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 10:51:25.406  INFO 39978 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 428 ms
2025-02-18 10:51:25.520  INFO 39978 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 10:51:25.617  INFO 39978 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 10:51:25.623  INFO 39978 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.886 seconds (JVM running for 1.262)
2025-02-18 10:51:25.678  INFO 39978 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb8009479] REGISTERED
2025-02-18 10:51:25.678  INFO 39978 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb8009479] CONNECT: /**********:23
2025-02-18 10:51:26.218  INFO 39978 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; noAbout=1; JSESSIONID=a_POJoxVmwW5grbNWocnAn_FTxcVXFLrIFoVtMoz; gray_version=********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=**********; HMACCOUNT=41403873BC771390; Hm_lpvt_0febd9e3cacb3f627ddac64d52caac39=**********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 10:51:26.221  INFO 39978 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 10:51:26.221  INFO 39978 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 10:51:26.222  INFO 39978 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 10:51:55.683  INFO 39978 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb8009479] CLOSE
2025-02-18 10:51:55.692  INFO 39978 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xb8009479] UNREGISTERED
2025-02-18 11:17:28.382  INFO 2789 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 2789 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 11:17:28.384  INFO 2789 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 11:17:28.891  INFO 2789 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 11:17:28.896  INFO 2789 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 11:17:28.896  INFO 2789 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 11:17:28.928  INFO 2789 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 11:17:28.929  INFO 2789 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 508 ms
2025-02-18 11:17:29.046  INFO 2789 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 11:17:29.153  INFO 2789 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 11:17:29.159  INFO 2789 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.135 seconds (JVM running for 2.16)
2025-02-18 11:17:29.211  INFO 2789 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x691694ae] REGISTERED
2025-02-18 11:17:29.211  INFO 2789 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x691694ae] CONNECT: /**********:23
2025-02-18 11:17:59.218  INFO 2789 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x691694ae] CLOSE
2025-02-18 11:17:59.230  INFO 2789 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x691694ae] UNREGISTERED
2025-02-18 11:22:21.588  INFO 2789 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; noAbout=1; gray_version=********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=**********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 11:22:21.592  INFO 2789 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 11:22:21.592  INFO 2789 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 11:22:21.593  INFO 2789 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 11:31:16.315  INFO 3220 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 3220 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 11:31:16.318  INFO 3220 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 11:31:16.778  INFO 3220 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 11:31:16.784  INFO 3220 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 11:31:16.784  INFO 3220 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 11:31:16.826  INFO 3220 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 11:31:16.826  INFO 3220 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 472 ms
2025-02-18 11:31:17.003  INFO 3220 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 11:31:17.158  INFO 3220 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 11:31:17.166  INFO 3220 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.159 seconds (JVM running for 1.698)
2025-02-18 11:31:17.236  INFO 3220 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xe756fcb4] REGISTERED
2025-02-18 11:31:17.236  INFO 3220 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xe756fcb4] CONNECT: /**********:23
2025-02-18 11:31:17.814  INFO 3220 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; noAbout=1; gray_version=********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=**********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 11:31:17.817  INFO 3220 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 11:31:17.818  INFO 3220 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 11:31:17.818  INFO 3220 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-02-18 11:31:47.240  INFO 3220 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xe756fcb4] CLOSE
2025-02-18 11:31:47.241  INFO 3220 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0xe756fcb4] UNREGISTERED
2025-02-18 11:31:53.919  INFO 3242 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 3242 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage)
2025-02-18 11:31:53.921  INFO 3242 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-02-18 11:31:54.375  INFO 3242 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-02-18 11:31:54.379  INFO 3242 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-02-18 11:31:54.380  INFO 3242 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-02-18 11:31:54.409  INFO 3242 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-02-18 11:31:54.409  INFO 3242 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 458 ms
2025-02-18 11:31:54.513  INFO 3242 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-02-18 11:31:54.611  INFO 3242 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-02-18 11:31:54.617  INFO 3242 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 0.936 seconds (JVM running for 1.464)
2025-02-18 11:31:54.663  INFO 3242 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x880a9b37] REGISTERED
2025-02-18 11:31:54.663  INFO 3242 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x880a9b37] CONNECT: /**********:23
2025-02-18 11:32:19.839  INFO 3242 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [**********; noAbout=1; gray_version=********; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=**********] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-02-18 11:32:19.842  INFO 3242 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-02-18 11:32:19.842  INFO 3242 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-02-18 11:32:19.843  INFO 3242 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-02-18 11:32:24.669  INFO 3242 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x880a9b37] CLOSE
2025-02-18 11:32:24.676  INFO 3242 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x880a9b37] UNREGISTERED
