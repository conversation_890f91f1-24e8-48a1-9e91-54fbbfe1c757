2025-08-13 15:18:36.177  INFO 79476 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 79476 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage_xa/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage_xa)
2025-08-13 15:18:36.196  INFO 79476 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-08-13 15:18:37.001  INFO 79476 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-13 15:18:37.007  INFO 79476 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-13 15:18:37.007  INFO 79476 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-13 15:18:37.045  INFO 79476 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-13 15:18:37.045  INFO 79476 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 792 ms
2025-08-13 15:18:37.184  INFO 79476 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 15:18:37.297  INFO 79476 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 15:18:37.304  INFO 79476 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.494 seconds (JVM running for 1.965)
2025-08-13 15:18:37.381  INFO 79476 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x76b71e5a] REGISTERED
2025-08-13 15:18:37.381  INFO 79476 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x76b71e5a] CONNECT: /**********:23
2025-08-13 15:18:50.514  INFO 79476 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [1736504637; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1754373091] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-08-13 15:18:50.518  INFO 79476 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 15:18:50.518  INFO 79476 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-13 15:18:50.520  INFO 79476 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-13 15:19:07.389  INFO 79476 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x76b71e5a] CLOSE
2025-08-13 15:19:07.398  INFO 79476 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x76b71e5a] UNREGISTERED
2025-08-13 15:33:29.594  INFO 79476 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 15:33:32.032  INFO 80466 --- [main] c.e.i.InOrOutStorageApplication          : Starting InOrOutStorageApplication using Java 1.8.0_352 on yizengmingdeMacBook-Pro.local with PID 80466 (/Users/<USER>/工作/IdeaProjects/inOrOutStorage_xa/target/classes started by yizengming in /Users/<USER>/工作/IdeaProjects/inOrOutStorage_xa)
2025-08-13 15:33:32.034  INFO 80466 --- [main] c.e.i.InOrOutStorageApplication          : The following profiles are active: pro
2025-08-13 15:33:32.989  INFO 80466 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-13 15:33:32.994  INFO 80466 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-13 15:33:32.995  INFO 80466 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.45]
2025-08-13 15:33:33.033  INFO 80466 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-13 15:33:33.033  INFO 80466 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 962 ms
2025-08-13 15:33:33.163  INFO 80466 --- [main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 15:33:33.295  INFO 80466 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 15:33:33.301  INFO 80466 --- [main] c.e.i.InOrOutStorageApplication          : Started InOrOutStorageApplication in 1.709 seconds (JVM running for 2.35)
2025-08-13 15:33:33.362  INFO 80466 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x33eab6ba] REGISTERED
2025-08-13 15:33:33.363  INFO 80466 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x33eab6ba] CONNECT: /**********:23
2025-08-13 15:33:41.751  INFO 80466 --- [http-nio-8080-exec-1] o.apache.tomcat.util.http.parser.Cookie  : A cookie header was received [1736504637; Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1754373091] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-08-13 15:33:41.756  INFO 80466 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 15:33:41.757  INFO 80466 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-13 15:33:41.759  INFO 80466 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-13 15:33:44.503  INFO 80466 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x33eab6ba] CLOSE
2025-08-13 15:33:44.505  INFO 80466 --- [nioEventLoopGroup-2-1] io.netty.handler.logging.LoggingHandler  : [id: 0x33eab6ba] UNREGISTERED
2025-08-13 15:33:44.523  INFO 80466 --- [SpringContextShutdownHook] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService 'applicationTaskExecutor'
